import 'package:flutter_test/flutter_test.dart';
import 'package:meditatingleo_webapp/core/security/web_security_utils.dart';

void main() {
  group('WebSecurityUtils', () {
    group('CSRF Protection', () {
      test('should generate valid CSRF token', () {
        // Act
        final token = WebSecurityUtils.generateCSRFToken();

        // Assert
        expect(token, isNotEmpty);
        expect(token.length, greaterThanOrEqualTo(32));
        expect(RegExp(r'^[a-zA-Z0-9]+$').hasMatch(token), isTrue);
      });

      test('should generate unique CSRF tokens', () {
        // Act
        final token1 = WebSecurityUtils.generateCSRFToken();
        final token2 = WebSecurityUtils.generateCSRFToken();

        // Assert
        expect(token1, isNot(equals(token2)));
      });

      test('should validate correct CSRF token', () {
        // Arrange
        final token = WebSecurityUtils.generateCSRFToken();
        WebSecurityUtils.setCSRFToken(token);

        // Act
        final isValid = WebSecurityUtils.validateCSRFToken(token);

        // Assert
        expect(isValid, isTrue);
      });

      test('should reject invalid CSRF token', () {
        // Arrange
        final validToken = WebSecurityUtils.generateCSRFToken();
        WebSecurityUtils.setCSRFToken(validToken);

        // Act
        final isValid = WebSecurityUtils.validateCSRFToken('invalid-token');

        // Assert
        expect(isValid, isFalse);
      });

      test('should reject empty CSRF token', () {
        // Arrange
        final validToken = WebSecurityUtils.generateCSRFToken();
        WebSecurityUtils.setCSRFToken(validToken);

        // Act
        final isValid = WebSecurityUtils.validateCSRFToken('');

        // Assert
        expect(isValid, isFalse);
      });
    });

    group('Input Sanitization', () {
      test('should sanitize HTML tags', () {
        // Arrange
        const input = '<script>alert("xss")</script>Hello World';

        // Act
        final sanitized = WebSecurityUtils.sanitizeInput(input);

        // Assert
        expect(sanitized,
            '&lt;script&gt;alert(&quot;xss&quot;)&lt;/script&gt;Hello World');
      });

      test('should sanitize special characters', () {
        // Arrange
        const input = 'Hello & "World" <test>';

        // Act
        final sanitized = WebSecurityUtils.sanitizeInput(input);

        // Assert
        expect(sanitized, 'Hello &amp; &quot;World&quot; &lt;test&gt;');
      });

      test('should handle empty input', () {
        // Act
        final sanitized = WebSecurityUtils.sanitizeInput('');

        // Assert
        expect(sanitized, '');
      });

      test('should handle null input', () {
        // Act
        final sanitized = WebSecurityUtils.sanitizeInput(null);

        // Assert
        expect(sanitized, '');
      });

      test('should preserve safe characters', () {
        // Arrange
        const input = 'Hello World 123 !@#\$%^*()_+-=[]{}|;:,./';

        // Act
        final sanitized = WebSecurityUtils.sanitizeInput(input);

        // Assert
        expect(sanitized, contains('Hello World 123'));
      });
    });

    group('Password Validation', () {
      test('should validate strong password', () {
        // Arrange
        const password = 'StrongP@ssw0rd123';

        // Act
        final result = WebSecurityUtils.validatePasswordStrength(password);

        // Assert
        expect(result.isValid, isTrue);
        expect(result.score, greaterThanOrEqualTo(4));
        expect(result.feedback, isEmpty);
      });

      test('should reject weak password', () {
        // Arrange
        const password = '123456';

        // Act
        final result = WebSecurityUtils.validatePasswordStrength(password);

        // Assert
        expect(result.isValid, isFalse);
        expect(result.score, lessThan(3));
        expect(result.feedback, isNotEmpty);
      });

      test('should require minimum length', () {
        // Arrange
        const password = 'Abc1!';

        // Act
        final result = WebSecurityUtils.validatePasswordStrength(password);

        // Assert
        expect(result.isValid, isFalse);
        expect(result.feedback,
            contains('Password must be at least 8 characters long'));
      });

      test('should require uppercase letter', () {
        // Arrange
        const password = 'password123!';

        // Act
        final result = WebSecurityUtils.validatePasswordStrength(password);

        // Assert
        expect(result.feedback,
            contains('Password must contain at least one uppercase letter'));
      });

      test('should require lowercase letter', () {
        // Arrange
        const password = 'PASSWORD123!';

        // Act
        final result = WebSecurityUtils.validatePasswordStrength(password);

        // Assert
        expect(result.feedback,
            contains('Password must contain at least one lowercase letter'));
      });

      test('should require number', () {
        // Arrange
        const password = 'Password!';

        // Act
        final result = WebSecurityUtils.validatePasswordStrength(password);

        // Assert
        expect(result.feedback,
            contains('Password must contain at least one number'));
      });

      test('should require special character', () {
        // Arrange
        const password = 'Password123';

        // Act
        final result = WebSecurityUtils.validatePasswordStrength(password);

        // Assert
        expect(result.feedback,
            contains('Password must contain at least one special character'));
      });
    });

    group('Email Validation', () {
      test('should validate correct email format', () {
        // Arrange
        const emails = [
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
        ];

        // Act & Assert
        for (final email in emails) {
          expect(WebSecurityUtils.isValidEmail(email), isTrue,
              reason: 'Failed for: $email');
        }
      });

      test('should reject invalid email format', () {
        // Arrange
        const emails = [
          'invalid-email',
          '@example.com',
          'user@',
          '<EMAIL>',
          'user@.com',
          'user@domain.',
          '',
        ];

        // Act & Assert
        for (final email in emails) {
          expect(WebSecurityUtils.isValidEmail(email), isFalse,
              reason: 'Should fail for: $email');
        }
      });
    });

    group('Secure Random Generation', () {
      test('should generate secure random string', () {
        // Act
        final random1 = WebSecurityUtils.generateSecureRandom(32);
        final random2 = WebSecurityUtils.generateSecureRandom(32);

        // Assert
        expect(random1.length, 32);
        expect(random2.length, 32);
        expect(random1, isNot(equals(random2)));
        expect(RegExp(r'^[a-zA-Z0-9]+$').hasMatch(random1), isTrue);
      });

      test('should generate different lengths', () {
        // Act
        final short = WebSecurityUtils.generateSecureRandom(8);
        final long = WebSecurityUtils.generateSecureRandom(64);

        // Assert
        expect(short.length, 8);
        expect(long.length, 64);
      });
    });

    group('URL Validation', () {
      test('should validate safe URLs', () {
        // Arrange
        const urls = [
          'https://example.com',
          'https://subdomain.example.com/path',
          'https://example.com/path?query=value',
          'https://example.com:8080/secure',
        ];

        // Act & Assert
        for (final url in urls) {
          expect(WebSecurityUtils.isSafeURL(url), isTrue,
              reason: 'Failed for: $url');
        }
      });

      test('should reject unsafe URLs', () {
        // Arrange
        const urls = [
          'javascript:alert("xss")',
          'data:text/html,<script>alert("xss")</script>',
          'http://example.com', // Non-HTTPS
          'ftp://example.com',
          'file:///etc/passwd',
          '',
        ];

        // Act & Assert
        for (final url in urls) {
          expect(WebSecurityUtils.isSafeURL(url), isFalse,
              reason: 'Should fail for: $url');
        }
      });
    });
  });
}
