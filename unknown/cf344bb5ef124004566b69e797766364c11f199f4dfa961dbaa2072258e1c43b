import 'package:freezed_annotation/freezed_annotation.dart';

part 'app_error.freezed.dart';

/// Represents different types of errors that can occur in the application.
///
/// This sealed class provides a type-safe way to handle different error scenarios
/// throughout the application, making error handling more predictable and maintainable.
///
/// Example usage:
/// ```dart
/// AppError error = AppError.network('Connection timeout');
/// error.when(
///   network: (message) => showNetworkErrorDialog(message),
///   database: (message) => showDatabaseErrorDialog(message),
///   authentication: (message) => redirectToLogin(),
///   validation: (field, message) => highlightField(field, message),
///   unknown: (message) => showGenericErrorDialog(message),
/// );
/// ```
@freezed
class AppError with _$AppError {
  /// Network-related errors (connection issues, timeouts, etc.)
  const factory AppError.network(String message) = NetworkError;

  /// Database-related errors (query failures, connection issues, etc.)
  const factory AppError.database(String message) = DatabaseError;

  /// Authentication and authorization errors
  const factory AppError.authentication(String message) = AuthenticationError;

  /// Input validation errors
  const factory AppError.validation(String field, String message) =
      ValidationError;

  /// Permission and access control errors
  const factory AppError.permission(String message) = PermissionError;

  /// Data not found errors
  const factory AppError.notFound(String resource) = NotFoundError;

  /// Conflict errors (data conflicts, duplicate entries, etc.)
  const factory AppError.conflict(String message) = ConflictError;

  /// Rate limiting errors
  const factory AppError.rateLimit(String message) = RateLimitError;

  /// Server errors (5xx responses)
  const factory AppError.server(String message) = ServerError;

  /// Client errors (4xx responses, except authentication and validation)
  const factory AppError.client(String message) = ClientError;

  /// Sync and offline-related errors
  const factory AppError.sync(String message) = SyncError;

  /// Cache-related errors
  const factory AppError.cache(String message) = CacheError;

  /// Storage-related errors (localStorage, sessionStorage, etc.)
  const factory AppError.storage(String message) = StorageError;

  /// Unknown or unexpected errors
  const factory AppError.unknown(String message) = UnknownError;
}

/// Extension methods for [AppError] to provide convenient utilities.
extension AppErrorExtensions on AppError {
  /// Returns the primary message from the error.
  String get message {
    return when(
      network: (message) => message,
      database: (message) => message,
      authentication: (message) => message,
      validation: (field, message) => message,
      permission: (message) => message,
      notFound: (resource) => resource,
      conflict: (message) => message,
      rateLimit: (message) => message,
      server: (message) => message,
      client: (message) => message,
      sync: (message) => message,
      cache: (message) => message,
      storage: (message) => message,
      unknown: (message) => message,
    );
  }

  /// Returns a user-friendly message for display in the UI.
  String get userMessage {
    return when(
      network: (message) =>
          'Network connection problem. Please check your internet connection.',
      database: (message) => 'Data access problem. Please try again.',
      authentication: (message) => 'Authentication required. Please sign in.',
      validation: (field, message) => message,
      permission: (message) =>
          'You don\'t have permission to perform this action.',
      notFound: (resource) => '$resource not found.',
      conflict: (message) =>
          'Data conflict occurred. Please refresh and try again.',
      rateLimit: (message) =>
          'Too many requests. Please wait a moment and try again.',
      server: (message) => 'Server error occurred. Please try again later.',
      client: (message) =>
          'Request error. Please check your input and try again.',
      sync: (message) =>
          'Sync problem. Your data will be synchronized when connection is restored.',
      cache: (message) => 'Cache error. Please refresh the page.',
      storage: (message) => 'Storage error. Please try again.',
      unknown: (message) => 'An unexpected error occurred. Please try again.',
    );
  }

  /// Returns the technical message for logging and debugging.
  String get technicalMessage {
    return when(
      network: (message) => 'Network Error: $message',
      database: (message) => 'Database Error: $message',
      authentication: (message) => 'Authentication Error: $message',
      validation: (field, message) => 'Validation Error [$field]: $message',
      permission: (message) => 'Permission Error: $message',
      notFound: (resource) => 'Not Found Error: $resource',
      conflict: (message) => 'Conflict Error: $message',
      rateLimit: (message) => 'Rate Limit Error: $message',
      server: (message) => 'Server Error: $message',
      client: (message) => 'Client Error: $message',
      sync: (message) => 'Sync Error: $message',
      cache: (message) => 'Cache Error: $message',
      storage: (message) => 'Storage Error: $message',
      unknown: (message) => 'Unknown Error: $message',
    );
  }

  /// Returns the error severity level for logging and monitoring.
  ErrorSeverity get severity {
    return when(
      network: (_) => ErrorSeverity.medium,
      database: (_) => ErrorSeverity.high,
      authentication: (_) => ErrorSeverity.medium,
      validation: (_, __) => ErrorSeverity.low,
      permission: (_) => ErrorSeverity.medium,
      notFound: (_) => ErrorSeverity.low,
      conflict: (_) => ErrorSeverity.medium,
      rateLimit: (_) => ErrorSeverity.low,
      server: (_) => ErrorSeverity.high,
      client: (_) => ErrorSeverity.low,
      sync: (_) => ErrorSeverity.medium,
      cache: (_) => ErrorSeverity.low,
      storage: (_) => ErrorSeverity.low,
      unknown: (_) => ErrorSeverity.high,
    );
  }

  /// Returns true if this error should be retried automatically.
  bool get isRetryable {
    return when(
      network: (_) => true,
      database: (_) => true,
      authentication: (_) => false,
      validation: (_, __) => false,
      permission: (_) => false,
      notFound: (_) => false,
      conflict: (_) => false,
      rateLimit: (_) => true,
      server: (_) => true,
      client: (_) => false,
      sync: (_) => true,
      cache: (_) => true,
      storage: (_) => true,
      unknown: (_) => false,
    );
  }

  /// Returns true if this error should be reported to crash analytics.
  bool get shouldReport {
    return when(
      network: (_) => false,
      database: (_) => true,
      authentication: (_) => false,
      validation: (_, __) => false,
      permission: (_) => false,
      notFound: (_) => false,
      conflict: (_) => false,
      rateLimit: (_) => false,
      server: (_) => true,
      client: (_) => false,
      sync: (_) => false,
      cache: (_) => false,
      storage: (_) => false,
      unknown: (_) => true,
    );
  }
}

/// Represents the severity level of an error.
enum ErrorSeverity {
  low,
  medium,
  high,
  critical,
}

/// Utility class for creating common [AppError] instances.
class AppErrorFactory {
  /// Creates a network error from an exception.
  static AppError fromNetworkException(Object exception) {
    return AppError.network(exception.toString());
  }

  /// Creates a database error from an exception.
  static AppError fromDatabaseException(Object exception) {
    return AppError.database(exception.toString());
  }

  /// Creates an authentication error for expired sessions.
  static AppError sessionExpired() {
    return const AppError.authentication(
        'Session expired. Please sign in again.');
  }

  /// Creates a validation error for required fields.
  static AppError requiredField(String fieldName) {
    return AppError.validation(fieldName, '$fieldName is required.');
  }

  /// Creates a validation error for invalid email format.
  static AppError invalidEmail() {
    return const AppError.validation(
        'email', 'Please enter a valid email address.');
  }

  /// Creates a validation error for weak passwords.
  static AppError weakPassword() {
    return const AppError.validation(
        'password', 'Password must be at least 8 characters long.');
  }

  /// Creates a not found error for specific resources.
  static AppError resourceNotFound(String resourceType, String identifier) {
    return AppError.notFound('$resourceType with ID $identifier');
  }

  /// Creates a conflict error for duplicate entries.
  static AppError duplicateEntry(String field) {
    return AppError.conflict('$field already exists.');
  }

  /// Creates an unknown error from any exception.
  static AppError fromException(Object exception, [StackTrace? stackTrace]) {
    return AppError.unknown('Unexpected error: ${exception.toString()}');
  }
}
