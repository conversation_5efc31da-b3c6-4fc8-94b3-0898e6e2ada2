import 'dart:html' as html;
import 'web_storage_interface.dart';

/// [WebStorageImpl] provides the actual web implementation using dart:html.
///
/// This implementation is used in the actual web application and provides
/// real browser storage functionality.
class WebStorageImpl implements WebStorageInterface {
  @override
  void setLocalStorage(String key, String value) {
    html.window.localStorage[key] = value;
  }

  @override
  String? getLocalStorage(String key) {
    return html.window.localStorage[key];
  }

  @override
  void removeLocalStorage(String key) {
    html.window.localStorage.remove(key);
  }

  @override
  void setSessionStorage(String key, String value) {
    html.window.sessionStorage[key] = value;
  }

  @override
  String? getSessionStorage(String key) {
    return html.window.sessionStorage[key];
  }

  @override
  void removeSessionStorage(String key) {
    html.window.sessionStorage.remove(key);
  }

  @override
  bool localStorageContainsKey(String key) {
    return html.window.localStorage.containsKey(key);
  }

  @override
  bool sessionStorageContainsKey(String key) {
    return html.window.sessionStorage.containsKey(key);
  }

  @override
  int get localStorageLength => html.window.localStorage.length;

  @override
  Iterable<String> get localStorageKeys => html.window.localStorage.keys;

  @override
  void clearSessionStorage() {
    html.window.sessionStorage.clear();
  }

  @override
  bool get isStorageSupported {
    try {
      const testKey = 'test_storage_support';
      html.window.localStorage[testKey] = 'test';
      html.window.localStorage.remove(testKey);
      return true;
    } catch (e) {
      return false;
    }
  }
}
