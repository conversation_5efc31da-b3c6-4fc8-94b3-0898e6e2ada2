import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:meditatingleo_webapp/core/providers/app_providers.dart';

void main() {
  group('AppNotifier', () {
    late ProviderContainer container;

    setUp(() {
      container = ProviderContainer();
    });

    tearDown(() {
      container.dispose();
    });

    test('should have initial state', () {
      // Act
      final state = container.read(appNotifierProvider);

      // Assert
      expect(state.hasValue, true);
      expect(state.value?.isInitialized, false);
      expect(state.value?.isLoading, false);
      expect(state.value?.hasError, false);
    });

    test('should manage theme mode', () {
      // Act
      final notifier = container.read(appNotifierProvider.notifier);

      // Test initial theme
      var state = container.read(appNotifierProvider);
      expect(state.value?.themeMode, AppThemeMode.system);

      // Test setting dark theme
      notifier.setThemeMode(AppThemeMode.dark);
      state = container.read(appNotifierProvider);
      expect(state.value?.themeMode, AppThemeMode.dark);

      // Test setting light theme
      notifier.setThemeMode(AppThemeMode.light);
      state = container.read(appNotifierProvider);
      expect(state.value?.themeMode, AppThemeMode.light);
    });

    test('should manage locale', () {
      final notifier = container.read(appNotifierProvider.notifier);

      // Test initial locale
      var state = container.read(appNotifierProvider);
      expect(state.value?.locale, 'en');

      // Test setting different locale
      notifier.setLocale('es');
      state = container.read(appNotifierProvider);
      expect(state.value?.locale, 'es');
    });

    test('should handle errors', () {
      final notifier = container.read(appNotifierProvider.notifier);

      // Test handling error
      notifier.handleError('Test error message');
      var state = container.read(appNotifierProvider);
      expect(state.value?.hasError, true);
      expect(state.value?.errorMessage, 'Test error message');

      // Test clearing error
      notifier.clearError();
      state = container.read(appNotifierProvider);
      expect(state.value?.hasError, false);
      expect(state.value?.errorMessage, null);
    });

    test('should manage loading state', () {
      final notifier = container.read(appNotifierProvider.notifier);

      // Test setting loading
      notifier.setLoading(true);
      var state = container.read(appNotifierProvider);
      expect(state.value?.isLoading, true);

      // Test clearing loading
      notifier.setLoading(false);
      state = container.read(appNotifierProvider);
      expect(state.value?.isLoading, false);
    });
  });

  group('NavigationNotifier', () {
    late ProviderContainer container;

    setUp(() {
      container = ProviderContainer();
    });

    tearDown(() {
      container.dispose();
    });

    test('should manage navigation state', () {
      final notifier = container.read(navigationNotifierProvider.notifier);

      // Test initial state
      var state = container.read(navigationNotifierProvider);
      expect(state.currentRoute, '/');
      expect(state.history, ['/']);

      // Test setting current route (doesn't add to history)
      notifier.setCurrentRoute('/journal');
      state = container.read(navigationNotifierProvider);
      expect(state.currentRoute, '/journal');
      expect(state.history, ['/']); // History unchanged

      // Test adding to history
      notifier.addToHistory('/settings');
      state = container.read(navigationNotifierProvider);
      expect(state.currentRoute, '/settings');
      expect(state.history.length, 2); // ['/', '/settings']
      expect(state.history.last, '/settings');
    });

    test('should handle navigation back', () {
      final notifier = container.read(navigationNotifierProvider.notifier);

      // Add some history
      notifier.addToHistory('/journal');
      notifier.addToHistory('/settings');

      var state = container.read(navigationNotifierProvider);
      expect(state.history.length, 3);
      expect(state.currentRoute, '/settings');

      // Go back
      notifier.goBack();
      state = container.read(navigationNotifierProvider);
      expect(state.currentRoute, '/journal');
      expect(state.history.length, 2);

      // Check can go back
      expect(notifier.canGoBack(), true);

      // Go back to root
      notifier.goBack();
      state = container.read(navigationNotifierProvider);
      expect(state.currentRoute, '/');
      expect(state.history.length, 1);
      expect(notifier.canGoBack(), false);
    });

    test('should clear history', () {
      final notifier = container.read(navigationNotifierProvider.notifier);

      // Add some history
      notifier.addToHistory('/journal');
      notifier.addToHistory('/settings');

      var state = container.read(navigationNotifierProvider);
      expect(state.history.length, 3);

      // Clear history
      notifier.clearHistory();
      state = container.read(navigationNotifierProvider);
      expect(state.history.length, 1);
      expect(state.history.first, state.currentRoute);
    });
  });

  group('RealTimeNotifier', () {
    late ProviderContainer container;

    setUp(() {
      container = ProviderContainer();
    });

    tearDown(() {
      container.dispose();
    });

    test('should manage real-time state', () {
      final notifier = container.read(realTimeNotifierProvider.notifier);

      // Test initial state
      var state = container.read(realTimeNotifierProvider);
      expect(state.isConnected, false);
      expect(state.activeSubscriptions.isEmpty, true);

      // Test setting connection status
      notifier.setConnectionStatus(true);
      state = container.read(realTimeNotifierProvider);
      expect(state.isConnected, true);

      // Test adding subscription
      notifier.addSubscription('journal_updates', 'journal_entries');
      state = container.read(realTimeNotifierProvider);
      expect(state.activeSubscriptions.containsKey('journal_updates'), true);
      expect(state.activeSubscriptions['journal_updates'], 'journal_entries');

      // Test removing subscription
      notifier.removeSubscription('journal_updates');
      state = container.read(realTimeNotifierProvider);
      expect(state.activeSubscriptions.containsKey('journal_updates'), false);
    });

    test('should track last update time', () {
      final notifier = container.read(realTimeNotifierProvider.notifier);

      final updateTime = DateTime.now();
      notifier.setLastUpdate(updateTime);

      final state = container.read(realTimeNotifierProvider);
      expect(state.lastUpdate, updateTime);
    });
  });
}
