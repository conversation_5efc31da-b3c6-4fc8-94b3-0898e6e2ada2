import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:meditatingleo_webapp/features/journal/providers/journal_providers.dart';

void main() {
  group('JournalNotifier', () {
    late ProviderContainer container;

    setUp(() {
      container = ProviderContainer();
    });

    tearDown(() {
      container.dispose();
    });

    test('should have initial state', () {
      // Act
      final state = container.read(journalNotifierProvider);

      // Assert
      expect(state.hasValue, true);
      expect(state.value?.entries.isEmpty, true);
    });
  });

  group('JournalFormNotifier', () {
    late ProviderContainer container;

    setUp(() {
      container = ProviderContainer();
    });

    tearDown(() {
      container.dispose();
    });

    test('should manage form state correctly', () {
      // Act
      final notifier = container.read(journalFormNotifierProvider.notifier);
      
      // Test initial state
      var state = container.read(journalFormNotifierProvider);
      expect(state.title, '');
      expect(state.content, '');
      expect(state.isValid, false);

      // Test updating title
      notifier.updateTitle('Test Title');
      state = container.read(journalFormNotifierProvider);
      expect(state.title, 'Test Title');
      expect(state.isValid, false); // Still invalid without content

      // Test updating content
      notifier.updateContent('Test Content');
      state = container.read(journalFormNotifierProvider);
      expect(state.content, 'Test Content');
      expect(state.isValid, true); // Now valid with both title and content

      // Test clearing form
      notifier.clearForm();
      state = container.read(journalFormNotifierProvider);
      expect(state.title, '');
      expect(state.content, '');
      expect(state.isValid, false);
    });

    test('should validate form correctly', () {
      final notifier = container.read(journalFormNotifierProvider.notifier);

      // Test with empty title
      notifier.updateTitle('');
      notifier.updateContent('Some content');
      var state = container.read(journalFormNotifierProvider);
      expect(state.isValid, false);

      // Test with empty content
      notifier.updateTitle('Some title');
      notifier.updateContent('');
      state = container.read(journalFormNotifierProvider);
      expect(state.isValid, false);

      // Test with both filled
      notifier.updateTitle('Some title');
      notifier.updateContent('Some content');
      state = container.read(journalFormNotifierProvider);
      expect(state.isValid, true);
    });

    test('should set form data for editing', () {
      final notifier = container.read(journalFormNotifierProvider.notifier);

      // Set form data
      notifier.setFormData('Edit Title', 'Edit Content');
      
      final state = container.read(journalFormNotifierProvider);
      expect(state.title, 'Edit Title');
      expect(state.content, 'Edit Content');
      expect(state.isValid, true);
    });
  });
}
