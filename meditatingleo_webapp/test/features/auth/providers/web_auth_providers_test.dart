import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:meditatingleo_webapp/features/auth/providers/web_auth_providers.dart';
import 'package:meditatingleo_webapp/features/auth/data/repositories/web_auth_repository.dart';
import 'package:meditatingleo_webapp/features/auth/data/models/web_user_model.dart';
import 'package:meditatingleo_webapp/features/auth/data/models/auth_session_model.dart';
import 'package:meditatingleo_webapp/shared/models/result.dart';
import 'package:meditatingleo_webapp/shared/models/app_error.dart';

@GenerateMocks([WebAuthRepository])
import 'web_auth_providers_test.mocks.dart';

void main() {
  group('WebAuthNotifier', () {
    late MockWebAuthRepository mockRepository;
    late ProviderContainer container;

    setUp(() {
      mockRepository = MockWebAuthRepository();
      container = ProviderContainer(
        overrides: [
          webAuthRepositoryProvider.overrideWithValue(mockRepository),
        ],
      );
    });

    tearDown(() {
      container.dispose();
    });

    group('signInWithEmail', () {
      test('should sign in successfully and update state', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'password123';
        final user = WebUserModel(
          id: 'test-user-id',
          email: email,
          createdAt: DateTime.now(),
        );
        final session = AuthSessionModel(
          accessToken: 'test-token',
          expiresIn: 3600,
          userId: 'test-user-id',
        );

        when(mockRepository.signInWithEmail(email, password))
            .thenAnswer((_) async => Result.success((user, session)));

        final notifier = container.read(webAuthNotifierProvider.notifier);

        // Act
        await notifier.signInWithEmail(email, password);

        // Assert
        final state = container.read(webAuthNotifierProvider);
        expect(state.hasValue, isTrue);
        expect(state.value?.user, equals(user));
        expect(state.value?.session, equals(session));
        expect(state.value?.isAuthenticated, isTrue);
        verify(mockRepository.signInWithEmail(email, password)).called(1);
      });

      test('should handle sign in failure', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'wrongpassword';
        final error = AppError.authentication('Invalid credentials');

        when(mockRepository.signInWithEmail(email, password))
            .thenAnswer((_) async => Result.failure(error));

        final notifier = container.read(webAuthNotifierProvider.notifier);

        // Act
        await notifier.signInWithEmail(email, password);

        // Assert
        final state = container.read(webAuthNotifierProvider);
        expect(state.hasError, isTrue);
        expect(state.error, equals(error));
        verify(mockRepository.signInWithEmail(email, password)).called(1);
      });

      test('should set loading state during sign in', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'password123';
        final user = WebUserModel(
          id: 'test-user-id',
          email: email,
          createdAt: DateTime.now(),
        );
        final session = AuthSessionModel(
          accessToken: 'test-token',
          expiresIn: 3600,
          userId: 'test-user-id',
        );

        when(mockRepository.signInWithEmail(email, password))
            .thenAnswer((_) async {
          await Future.delayed(const Duration(milliseconds: 100));
          return Result.success((user, session));
        });

        final notifier = container.read(webAuthNotifierProvider.notifier);

        // Act
        final future = notifier.signInWithEmail(email, password);

        // Assert loading state
        final loadingState = container.read(webAuthNotifierProvider);
        expect(loadingState.isLoading, isTrue);

        await future;

        // Assert final state
        final finalState = container.read(webAuthNotifierProvider);
        expect(finalState.hasValue, isTrue);
        expect(finalState.value?.isAuthenticated, isTrue);
      });
    });

    group('signUp', () {
      test('should sign up successfully', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'password123';
        const name = 'Test User';
        final user = WebUserModel(
          id: 'test-user-id',
          email: email,
          name: name,
          createdAt: DateTime.now(),
        );

        when(mockRepository.signUp(email, password, name))
            .thenAnswer((_) async => Result.success(user));

        final notifier = container.read(webAuthNotifierProvider.notifier);

        // Act
        await notifier.signUp(email, password, name);

        // Assert
        final state = container.read(webAuthNotifierProvider);
        expect(state.hasValue, isTrue);
        expect(state.value?.user, equals(user));
        expect(state.value?.isAuthenticated, isFalse); // Email not confirmed yet
        verify(mockRepository.signUp(email, password, name)).called(1);
      });

      test('should handle sign up failure', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'password123';
        const name = 'Test User';
        final error = AppError.authentication('Email already exists');

        when(mockRepository.signUp(email, password, name))
            .thenAnswer((_) async => Result.failure(error));

        final notifier = container.read(webAuthNotifierProvider.notifier);

        // Act
        await notifier.signUp(email, password, name);

        // Assert
        final state = container.read(webAuthNotifierProvider);
        expect(state.hasError, isTrue);
        expect(state.error, equals(error));
        verify(mockRepository.signUp(email, password, name)).called(1);
      });
    });

    group('signOut', () {
      test('should sign out successfully and clear state', () async {
        // Arrange
        when(mockRepository.signOut())
            .thenAnswer((_) async => Result.success(null));

        final notifier = container.read(webAuthNotifierProvider.notifier);

        // Set initial authenticated state
        final user = WebUserModel(
          id: 'test-user-id',
          email: '<EMAIL>',
          createdAt: DateTime.now(),
        );
        final session = AuthSessionModel(
          accessToken: 'test-token',
          expiresIn: 3600,
          userId: 'test-user-id',
        );

        when(mockRepository.signInWithEmail(any, any))
            .thenAnswer((_) async => Result.success((user, session)));

        await notifier.signInWithEmail('<EMAIL>', 'password123');

        // Act
        await notifier.signOut();

        // Assert
        final state = container.read(webAuthNotifierProvider);
        expect(state.hasValue, isTrue);
        expect(state.value?.user, isNull);
        expect(state.value?.session, isNull);
        expect(state.value?.isAuthenticated, isFalse);
        verify(mockRepository.signOut()).called(1);
      });
    });

    group('resetPassword', () {
      test('should send password reset email successfully', () async {
        // Arrange
        const email = '<EMAIL>';

        when(mockRepository.resetPassword(email))
            .thenAnswer((_) async => Result.success(null));

        final notifier = container.read(webAuthNotifierProvider.notifier);

        // Act
        final result = await notifier.resetPassword(email);

        // Assert
        expect(result.isSuccess, isTrue);
        verify(mockRepository.resetPassword(email)).called(1);
      });

      test('should handle password reset failure', () async {
        // Arrange
        const email = '<EMAIL>';
        final error = AppError.authentication('User not found');

        when(mockRepository.resetPassword(email))
            .thenAnswer((_) async => Result.failure(error));

        final notifier = container.read(webAuthNotifierProvider.notifier);

        // Act
        final result = await notifier.resetPassword(email);

        // Assert
        expect(result.isFailure, isTrue);
        expect(result.error, equals(error));
        verify(mockRepository.resetPassword(email)).called(1);
      });
    });

    group('refreshSession', () {
      test('should refresh session successfully', () async {
        // Arrange
        final newSession = AuthSessionModel(
          accessToken: 'new-access-token',
          refreshToken: 'new-refresh-token',
          expiresIn: 3600,
          userId: 'test-user-id',
        );

        when(mockRepository.refreshSession())
            .thenAnswer((_) async => Result.success(newSession));

        final notifier = container.read(webAuthNotifierProvider.notifier);

        // Act
        await notifier.refreshSession();

        // Assert
        final state = container.read(webAuthNotifierProvider);
        expect(state.hasValue, isTrue);
        expect(state.value?.session, equals(newSession));
        verify(mockRepository.refreshSession()).called(1);
      });
    });
  });
}
