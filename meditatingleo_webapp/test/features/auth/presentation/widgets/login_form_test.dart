import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:meditatingleo_webapp/features/auth/presentation/widgets/login_form.dart';
import 'package:meditatingleo_webapp/features/auth/providers/web_auth_providers.dart';
import 'package:meditatingleo_webapp/features/auth/data/repositories/web_auth_repository.dart';
import 'package:meditatingleo_webapp/features/auth/data/models/web_user_model.dart';
import 'package:meditatingleo_webapp/features/auth/data/models/auth_session_model.dart';
import 'package:meditatingleo_webapp/shared/models/result.dart';
import 'package:meditatingleo_webapp/shared/models/app_error.dart';

@GenerateMocks([WebAuthRepository])
import 'login_form_test.mocks.dart';

void main() {
  group('LoginForm Widget Tests', () {
    late MockWebAuthRepository mockRepository;

    setUp(() {
      mockRepository = MockWebAuthRepository();
    });

    Widget createTestWidget({VoidCallback? onSuccess}) {
      return ProviderScope(
        overrides: [
          webAuthRepositoryProvider.overrideWithValue(mockRepository),
        ],
        child: MaterialApp(
          home: Scaffold(
            body: LoginForm(onSuccess: onSuccess),
          ),
        ),
      );
    }

    group('UI Elements', () {
      testWidgets('should display all form elements',
          (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());

        // Assert
        expect(find.text('Sign In'), findsOneWidget);
        expect(find.text('Email'), findsOneWidget);
        expect(find.text('Password'), findsOneWidget);
        expect(find.text('Remember me'), findsOneWidget);
        expect(find.text('Forgot password?'), findsOneWidget);
        expect(find.byType(TextFormField), findsNWidgets(2));
        expect(find.byType(Checkbox), findsOneWidget);
        expect(find.byType(FilledButton), findsOneWidget);
      });

      testWidgets('should have proper form field labels',
          (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());

        // Assert
        final emailField = find.widgetWithText(TextFormField, 'Email');
        final passwordField = find.widgetWithText(TextFormField, 'Password');

        expect(emailField, findsOneWidget);
        expect(passwordField, findsOneWidget);
      });

      testWidgets('should have password field obscured by default',
          (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());

        // Assert
        expect(find.byIcon(Icons.visibility_off), findsOneWidget);
        expect(find.byIcon(Icons.visibility), findsNothing);
      });

      testWidgets('should show password visibility toggle',
          (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());

        // Assert
        expect(find.byIcon(Icons.visibility_off), findsOneWidget);
      });
    });

    group('Form Validation', () {
      testWidgets('should show error for empty email',
          (WidgetTester tester) async {
        // Arrange
        await tester.pumpWidget(createTestWidget());

        // Act
        await tester.tap(find.byType(FilledButton));
        await tester.pump();

        // Assert
        expect(find.text('Email is required'), findsOneWidget);
      });

      testWidgets('should show error for invalid email format',
          (WidgetTester tester) async {
        // Arrange
        await tester.pumpWidget(createTestWidget());

        // Act
        await tester.enterText(
          find.widgetWithText(TextFormField, 'Email'),
          'invalid-email',
        );
        await tester.tap(find.byType(FilledButton));
        await tester.pump();

        // Assert
        expect(find.text('Please enter a valid email'), findsOneWidget);
      });

      testWidgets('should show error for empty password',
          (WidgetTester tester) async {
        // Arrange
        await tester.pumpWidget(createTestWidget());

        // Act
        await tester.enterText(
          find.widgetWithText(TextFormField, 'Email'),
          '<EMAIL>',
        );
        await tester.tap(find.byType(FilledButton));
        await tester.pump();

        // Assert
        expect(find.text('Password is required'), findsOneWidget);
      });

      testWidgets('should show error for short password',
          (WidgetTester tester) async {
        // Arrange
        await tester.pumpWidget(createTestWidget());

        // Act
        await tester.enterText(
          find.widgetWithText(TextFormField, 'Email'),
          '<EMAIL>',
        );
        await tester.enterText(
          find.widgetWithText(TextFormField, 'Password'),
          '123',
        );
        await tester.tap(find.byType(FilledButton));
        await tester.pump();

        // Assert
        expect(find.text('Password must be at least 8 characters'),
            findsOneWidget);
      });

      testWidgets('should not show errors for valid input',
          (WidgetTester tester) async {
        // Arrange
        when(mockRepository.signInWithEmail(any, any)).thenAnswer(
            (_) async => Result.failure(AppError.network('Test error')));

        await tester.pumpWidget(createTestWidget());

        // Act
        await tester.enterText(
          find.widgetWithText(TextFormField, 'Email'),
          '<EMAIL>',
        );
        await tester.enterText(
          find.widgetWithText(TextFormField, 'Password'),
          'password123',
        );
        await tester.tap(find.byType(FilledButton));
        await tester.pump();

        // Assert
        expect(find.text('Email is required'), findsNothing);
        expect(find.text('Please enter a valid email'), findsNothing);
        expect(find.text('Password is required'), findsNothing);
        expect(
            find.text('Password must be at least 8 characters'), findsNothing);
      });
    });

    group('User Interactions', () {
      testWidgets('should toggle password visibility',
          (WidgetTester tester) async {
        // Arrange
        await tester.pumpWidget(createTestWidget());

        // Act
        await tester.tap(find.byIcon(Icons.visibility_off));
        await tester.pump();

        // Assert
        expect(find.byIcon(Icons.visibility), findsOneWidget);
        expect(find.byIcon(Icons.visibility_off), findsNothing);
      });

      testWidgets('should toggle remember me checkbox',
          (WidgetTester tester) async {
        // Arrange
        await tester.pumpWidget(createTestWidget());

        // Act
        await tester.tap(find.byType(Checkbox));
        await tester.pump();

        // Assert
        final checkbox = tester.widget<Checkbox>(find.byType(Checkbox));
        expect(checkbox.value, isTrue);
      });

      testWidgets('should call onSuccess callback on successful login',
          (WidgetTester tester) async {
        // Arrange
        bool onSuccessCalled = false;
        final user = WebUserModel(
          id: 'test-user-id',
          email: '<EMAIL>',
          createdAt: DateTime.now(),
        );
        final session = AuthSessionModel.createTestSession();

        when(mockRepository.signInWithEmail(any, any))
            .thenAnswer((_) async => Result.success((user, session)));

        await tester.pumpWidget(createTestWidget(
          onSuccess: () => onSuccessCalled = true,
        ));

        // Act
        await tester.enterText(
          find.widgetWithText(TextFormField, 'Email'),
          '<EMAIL>',
        );
        await tester.enterText(
          find.widgetWithText(TextFormField, 'Password'),
          'password123',
        );
        await tester.tap(find.byType(FilledButton));
        await tester.pumpAndSettle();

        // Assert
        expect(onSuccessCalled, isTrue);
      });

      testWidgets('should show loading state during login',
          (WidgetTester tester) async {
        // Arrange
        final user = WebUserModel(
          id: 'test-user-id',
          email: '<EMAIL>',
          createdAt: DateTime.now(),
        );
        final session = AuthSessionModel.createTestSession();

        when(mockRepository.signInWithEmail(any, any)).thenAnswer((_) async {
          await Future.delayed(const Duration(milliseconds: 100));
          return Result.success((user, session));
        });

        await tester.pumpWidget(createTestWidget());

        // Act
        await tester.enterText(
          find.widgetWithText(TextFormField, 'Email'),
          '<EMAIL>',
        );
        await tester.enterText(
          find.widgetWithText(TextFormField, 'Password'),
          'password123',
        );
        await tester.tap(find.byType(FilledButton));
        await tester.pump();

        // Assert
        expect(find.byType(CircularProgressIndicator), findsOneWidget);
        expect(find.text('Signing in...'), findsOneWidget);
      });

      testWidgets('should show error message on login failure',
          (WidgetTester tester) async {
        // Arrange
        when(mockRepository.signInWithEmail(any, any)).thenAnswer((_) async =>
            Result.failure(AppError.authentication('Invalid credentials')));

        await tester.pumpWidget(createTestWidget());

        // Act
        await tester.enterText(
          find.widgetWithText(TextFormField, 'Email'),
          '<EMAIL>',
        );
        await tester.enterText(
          find.widgetWithText(TextFormField, 'Password'),
          'wrongpassword',
        );
        await tester.tap(find.byType(FilledButton));
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Invalid credentials'), findsOneWidget);
      });
    });

    group('Responsive Design', () {
      testWidgets('should adapt to mobile layout', (WidgetTester tester) async {
        // Arrange
        tester.view.physicalSize = const Size(400, 800);
        tester.view.devicePixelRatio = 1.0;

        // Act
        await tester.pumpWidget(createTestWidget());

        // Assert
        expect(find.byType(LoginForm), findsOneWidget);
        // Form should be displayed in mobile layout
      });

      testWidgets('should adapt to desktop layout',
          (WidgetTester tester) async {
        // Arrange
        tester.view.physicalSize = const Size(1200, 800);
        tester.view.devicePixelRatio = 1.0;

        // Act
        await tester.pumpWidget(createTestWidget());

        // Assert
        expect(find.byType(LoginForm), findsOneWidget);
        // Form should be displayed in desktop layout
      });
    });
  });
}
