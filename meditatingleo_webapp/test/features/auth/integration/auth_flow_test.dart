import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:meditatingleo_webapp/features/auth/presentation/pages/login_page.dart';
import 'package:meditatingleo_webapp/features/auth/presentation/pages/register_page.dart';
import 'package:meditatingleo_webapp/features/auth/data/repositories/web_auth_repository.dart';
import 'package:meditatingleo_webapp/features/auth/data/models/web_user_model.dart';
import 'package:meditatingleo_webapp/features/auth/data/models/auth_session_model.dart';
import 'package:meditatingleo_webapp/features/auth/providers/web_auth_providers.dart';
import 'package:meditatingleo_webapp/shared/models/result.dart';
import 'package:meditatingleo_webapp/shared/models/app_error.dart';

@GenerateMocks([WebAuthRepository])
import 'auth_flow_test.mocks.dart';

void main() {
  group('Authentication Flow Integration Tests', () {
    late MockWebAuthRepository mockRepository;

    setUp(() {
      mockRepository = MockWebAuthRepository();
    });

    Widget createTestApp({Widget? home}) {
      return ProviderScope(
        overrides: [
          webAuthRepositoryProvider.overrideWithValue(mockRepository),
        ],
        child: MaterialApp(
          home: home ?? const LoginPage(),
          routes: {
            '/login': (context) => const LoginPage(),
            '/register': (context) => const RegisterPage(),
            '/home': (context) => const Scaffold(
                  body: Center(child: Text('Home Page')),
                ),
          },
        ),
      );
    }

    group('Login Flow', () {
      testWidgets('should complete successful login flow',
          (WidgetTester tester) async {
        // Arrange
        final user = WebUserModel(
          id: 'test-user-id',
          email: '<EMAIL>',
          name: 'Test User',
          createdAt: DateTime.now(),
          emailConfirmedAt: DateTime.now(),
        );
        final session = AuthSessionModel(
          accessToken: 'test-access-token',
          refreshToken: 'test-refresh-token',
          expiresIn: 3600,
          userId: 'test-user-id',
        );

        when(mockRepository.signInWithEmail('<EMAIL>', 'password123'))
            .thenAnswer((_) async => Result.success((user, session)));

        await tester.pumpWidget(createTestApp());

        // Act - Fill in login form
        await tester.enterText(
          find.widgetWithText(TextFormField, 'Email'),
          '<EMAIL>',
        );
        await tester.enterText(
          find.widgetWithText(TextFormField, 'Password'),
          'password123',
        );

        // Submit form
        await tester.tap(find.text('Sign In'));
        await tester.pumpAndSettle();

        // Assert
        verify(mockRepository.signInWithEmail(
                '<EMAIL>', 'password123'))
            .called(1);
        // Should navigate to home or show success state
      });

      testWidgets('should handle login failure gracefully',
          (WidgetTester tester) async {
        // Arrange
        when(mockRepository.signInWithEmail(
                '<EMAIL>', 'wrongpassword'))
            .thenAnswer((_) async =>
                Result.failure(AppError.authentication('Invalid credentials')));

        await tester.pumpWidget(createTestApp());

        // Act
        await tester.enterText(
          find.widgetWithText(TextFormField, 'Email'),
          '<EMAIL>',
        );
        await tester.enterText(
          find.widgetWithText(TextFormField, 'Password'),
          'wrongpassword',
        );
        await tester.tap(find.text('Sign In'));
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Invalid credentials'), findsOneWidget);
        verify(mockRepository.signInWithEmail(
                '<EMAIL>', 'wrongpassword'))
            .called(1);
      });

      testWidgets('should show loading state during login',
          (WidgetTester tester) async {
        // Arrange
        final user = WebUserModel(
          id: 'test-user-id',
          email: '<EMAIL>',
          createdAt: DateTime.now(),
          emailConfirmedAt: DateTime.now(),
        );
        final session = AuthSessionModel.createTestSession();

        when(mockRepository.signInWithEmail(any, any)).thenAnswer((_) async {
          await Future.delayed(const Duration(milliseconds: 500));
          return Result.success((user, session));
        });

        await tester.pumpWidget(createTestApp());

        // Act
        await tester.enterText(
          find.widgetWithText(TextFormField, 'Email'),
          '<EMAIL>',
        );
        await tester.enterText(
          find.widgetWithText(TextFormField, 'Password'),
          'password123',
        );
        await tester.tap(find.text('Sign In'));
        await tester.pump();

        // Assert
        expect(find.byType(CircularProgressIndicator), findsOneWidget);
        expect(find.text('Signing in...'), findsOneWidget);

        // Wait for completion
        await tester.pumpAndSettle();
      });
    });

    group('Registration Flow', () {
      testWidgets('should complete successful registration flow',
          (WidgetTester tester) async {
        // Arrange
        final user = WebUserModel(
          id: 'test-user-id',
          email: '<EMAIL>',
          name: 'New User',
          createdAt: DateTime.now(),
        );

        when(mockRepository.signUp(
                '<EMAIL>', 'password123', 'New User'))
            .thenAnswer((_) async => Result.success(user));

        await tester.pumpWidget(createTestApp(home: const RegisterPage()));

        // Act
        await tester.enterText(
          find.widgetWithText(TextFormField, 'Name'),
          'New User',
        );
        await tester.enterText(
          find.widgetWithText(TextFormField, 'Email'),
          '<EMAIL>',
        );
        await tester.enterText(
          find.widgetWithText(TextFormField, 'Password'),
          'password123',
        );
        await tester.enterText(
          find.widgetWithText(TextFormField, 'Confirm Password'),
          'password123',
        );
        await tester.tap(find.text('Create Account'));
        await tester.pumpAndSettle();

        // Assert
        verify(mockRepository.signUp(
                '<EMAIL>', 'password123', 'New User'))
            .called(1);
      });

      testWidgets('should validate password confirmation',
          (WidgetTester tester) async {
        // Arrange
        await tester.pumpWidget(createTestApp(home: const RegisterPage()));

        // Act
        await tester.enterText(
          find.widgetWithText(TextFormField, 'Name'),
          'New User',
        );
        await tester.enterText(
          find.widgetWithText(TextFormField, 'Email'),
          '<EMAIL>',
        );
        await tester.enterText(
          find.widgetWithText(TextFormField, 'Password'),
          'password123',
        );
        await tester.enterText(
          find.widgetWithText(TextFormField, 'Confirm Password'),
          'differentpassword',
        );
        await tester.tap(find.text('Create Account'));
        await tester.pump();

        // Assert
        expect(find.text('Passwords do not match'), findsOneWidget);
        verifyNever(mockRepository.signUp(any, any, any));
      });
    });

    group('Password Reset Flow', () {
      testWidgets('should send password reset email',
          (WidgetTester tester) async {
        // Arrange
        when(mockRepository.resetPassword('<EMAIL>'))
            .thenAnswer((_) async => Result.success(null));

        await tester.pumpWidget(createTestApp());

        // Act
        await tester.tap(find.text('Forgot password?'));
        await tester.pumpAndSettle();

        await tester.enterText(
          find.widgetWithText(TextFormField, 'Email'),
          '<EMAIL>',
        );
        await tester.tap(find.text('Send Reset Email'));
        await tester.pumpAndSettle();

        // Assert
        verify(mockRepository.resetPassword('<EMAIL>')).called(1);
        expect(find.text('Password reset email sent'), findsOneWidget);
      });
    });

    group('Session Management', () {
      testWidgets('should handle session expiration',
          (WidgetTester tester) async {
        // Arrange
        final expiredSession = AuthSessionModel(
          accessToken: 'expired-token',
          expiresIn: 3600,
          expiresAt: DateTime.now().subtract(const Duration(hours: 1)),
          userId: 'test-user-id',
        );

        when(mockRepository.refreshSession()).thenAnswer((_) async =>
            Result.failure(AppError.authentication('Session expired')));

        await tester.pumpWidget(createTestApp());

        // Simulate expired session state
        // This would typically be handled by the auth provider

        // Assert
        // Should redirect to login or show session expired message
      });

      testWidgets('should refresh session automatically',
          (WidgetTester tester) async {
        // Arrange
        final newSession = AuthSessionModel(
          accessToken: 'new-access-token',
          refreshToken: 'new-refresh-token',
          expiresIn: 3600,
          userId: 'test-user-id',
        );

        when(mockRepository.refreshSession())
            .thenAnswer((_) async => Result.success(newSession));

        await tester.pumpWidget(createTestApp());

        // This would be triggered by the session manager
        // when the session is about to expire

        // Assert
        // Session should be refreshed automatically
      });
    });

    group('Remember Me Functionality', () {
      testWidgets('should persist login with remember me',
          (WidgetTester tester) async {
        // Arrange
        final user = WebUserModel(
          id: 'test-user-id',
          email: '<EMAIL>',
          createdAt: DateTime.now(),
          emailConfirmedAt: DateTime.now(),
        );
        final session = AuthSessionModel(
          accessToken: 'test-access-token',
          expiresIn: 3600,
          userId: 'test-user-id',
        );

        when(mockRepository.signInWithEmail('<EMAIL>', 'password123'))
            .thenAnswer((_) async => Result.success((user, session)));

        await tester.pumpWidget(createTestApp());

        // Act
        await tester.enterText(
          find.widgetWithText(TextFormField, 'Email'),
          '<EMAIL>',
        );
        await tester.enterText(
          find.widgetWithText(TextFormField, 'Password'),
          'password123',
        );

        // Check remember me
        await tester.tap(find.byType(Checkbox));
        await tester.pump();

        await tester.tap(find.text('Sign In'));
        await tester.pumpAndSettle();

        // Assert
        // Should store persistent session data
        verify(mockRepository.signInWithEmail(
                '<EMAIL>', 'password123'))
            .called(1);
      });
    });

    group('Navigation Flow', () {
      testWidgets('should navigate between auth pages',
          (WidgetTester tester) async {
        // Arrange
        await tester.pumpWidget(createTestApp());

        // Act - Navigate to register
        await tester.tap(find.text('Create account'));
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Create Account'), findsOneWidget);
        expect(find.text('Sign In'), findsNothing);

        // Act - Navigate back to login
        await tester.tap(find.text('Already have an account? Sign in'));
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Sign In'), findsOneWidget);
        expect(find.text('Create Account'), findsNothing);
      });
    });
  });
}
