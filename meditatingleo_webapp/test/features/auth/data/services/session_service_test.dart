import 'package:flutter_test/flutter_test.dart';
import 'package:meditatingleo_webapp/features/auth/data/services/web_session_service.dart';
import 'package:meditatingleo_webapp/features/auth/data/services/web_storage_mock.dart';
import 'package:meditatingleo_webapp/features/auth/data/models/auth_session_model.dart';

void main() {
  group('WebSessionService', () {
    late WebSessionService sessionService;
    late WebStorageMock mockStorage;

    setUp(() {
      mockStorage = WebStorageMock();
      sessionService = WebSessionService(mockStorage);
    });

    tearDown(() {
      mockStorage.clearAll();
    });

    test('should store session in localStorage when rememberMe is true', () async {
      // Arrange
      final session = AuthSessionModel.createTestSession();

      // Act
      final result = await sessionService.storeSession(session, rememberMe: true);

      // Assert
      result.when(
        success: (_) {
          expect(mockStorage.localStorageContainsKey('meditatingleo_session'), isTrue);
          expect(mockStorage.getLocalStorage('meditatingleo_remember_me'), 'true');
        },
        failure: (error) => fail('Expected success but got error: $error'),
      );
    });

    test('should store session in sessionStorage when rememberMe is false', () async {
      // Arrange
      final session = AuthSessionModel.createTestSession();

      // Act
      final result = await sessionService.storeSession(session, rememberMe: false);

      // Assert
      result.when(
        success: (_) {
          expect(mockStorage.sessionStorageContainsKey('meditatingleo_session'), isTrue);
          expect(mockStorage.localStorageContainsKey('meditatingleo_remember_me'), isFalse);
        },
        failure: (error) => fail('Expected success but got error: $error'),
      );
    });

    test('should retrieve session from localStorage', () async {
      // Arrange
      final session = AuthSessionModel.createTestSession();
      await sessionService.storeSession(session, rememberMe: true);

      // Act
      final result = await sessionService.getStoredSession();

      // Assert
      result.when(
        success: (data) {
          expect(data, isNotNull);
          expect(data!.accessToken, session.accessToken);
        },
        failure: (error) => fail('Expected success but got error: $error'),
      );
    });

    test('should return null if no session is stored', () async {
      // Act
      final result = await sessionService.getStoredSession();

      // Assert
      result.when(
        success: (data) => expect(data, isNull),
        failure: (error) => fail('Expected success but got error: $error'),
      );
    });

    test('should check if remember me is enabled', () async {
      // Arrange
      final session = AuthSessionModel.createTestSession();
      await sessionService.storeSession(session, rememberMe: true);

      // Act
      final isEnabled = sessionService.isRememberMeEnabled();

      // Assert
      expect(isEnabled, isTrue);
    });

    test('should check if storage is supported', () {
      // Act
      final isSupported = sessionService.isStorageSupported();

      // Assert
      expect(isSupported, isTrue);
    });
  });
}
