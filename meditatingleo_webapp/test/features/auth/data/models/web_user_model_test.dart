import 'package:flutter_test/flutter_test.dart';
import 'package:meditatingleo_webapp/features/auth/data/models/web_user_model.dart';

void main() {
  group('WebUserModel', () {
    const testUserData = {
      'id': 'test-user-id',
      'email': '<EMAIL>',
      'name': 'Test User',
      'avatar_url': 'https://example.com/avatar.jpg',
      'created_at': '2024-01-01T00:00:00Z',
      'last_sign_in_at': '2024-01-01T12:00:00Z',
      'email_confirmed_at': '2024-01-01T00:30:00Z',
    };

    group('fromJson', () {
      test('should create WebUserModel from valid JSON', () {
        // Act
        final user = WebUserModel.fromJson(testUserData);

        // Assert
        expect(user.id, 'test-user-id');
        expect(user.email, '<EMAIL>');
        expect(user.name, 'Test User');
        expect(user.avatarUrl, 'https://example.com/avatar.jpg');
        expect(user.createdAt, DateTime.parse('2024-01-01T00:00:00Z'));
        expect(user.lastSignInAt, DateTime.parse('2024-01-01T12:00:00Z'));
        expect(user.emailConfirmedAt, DateTime.parse('2024-01-01T00:30:00Z'));
      });

      test('should handle null optional fields', () {
        // Arrange
        final minimalData = {
          'id': 'test-user-id',
          'email': '<EMAIL>',
          'created_at': '2024-01-01T00:00:00Z',
        };

        // Act
        final user = WebUserModel.fromJson(minimalData);

        // Assert
        expect(user.id, 'test-user-id');
        expect(user.email, '<EMAIL>');
        expect(user.name, isNull);
        expect(user.avatarUrl, isNull);
        expect(user.lastSignInAt, isNull);
        expect(user.emailConfirmedAt, isNull);
      });
    });

    group('toJson', () {
      test('should convert WebUserModel to JSON', () {
        // Arrange
        final user = WebUserModel(
          id: 'test-user-id',
          email: '<EMAIL>',
          name: 'Test User',
          avatarUrl: 'https://example.com/avatar.jpg',
          createdAt: DateTime.parse('2024-01-01T00:00:00Z'),
          lastSignInAt: DateTime.parse('2024-01-01T12:00:00Z'),
          emailConfirmedAt: DateTime.parse('2024-01-01T00:30:00Z'),
        );

        // Act
        final json = user.toJson();

        // Assert
        expect(json['id'], 'test-user-id');
        expect(json['email'], '<EMAIL>');
        expect(json['name'], 'Test User');
        expect(json['avatar_url'], 'https://example.com/avatar.jpg');
        expect(json['created_at'], '2024-01-01T00:00:00.000Z');
        expect(json['last_sign_in_at'], '2024-01-01T12:00:00.000Z');
        expect(json['email_confirmed_at'], '2024-01-01T00:30:00.000Z');
      });
    });

    group('copyWith', () {
      test('should create copy with updated fields', () {
        // Arrange
        final originalUser = WebUserModel(
          id: 'test-user-id',
          email: '<EMAIL>',
          name: 'Test User',
          createdAt: DateTime.parse('2024-01-01T00:00:00Z'),
        );

        // Act
        final updatedUser = originalUser.copyWith(
          name: 'Updated User',
          avatarUrl: 'https://example.com/new-avatar.jpg',
        );

        // Assert
        expect(updatedUser.id, 'test-user-id');
        expect(updatedUser.email, '<EMAIL>');
        expect(updatedUser.name, 'Updated User');
        expect(updatedUser.avatarUrl, 'https://example.com/new-avatar.jpg');
        expect(updatedUser.createdAt, originalUser.createdAt);
      });
    });

    group('equality', () {
      test('should be equal when all fields match', () {
        // Arrange
        final user1 = WebUserModel(
          id: 'test-user-id',
          email: '<EMAIL>',
          name: 'Test User',
          createdAt: DateTime.parse('2024-01-01T00:00:00Z'),
        );

        final user2 = WebUserModel(
          id: 'test-user-id',
          email: '<EMAIL>',
          name: 'Test User',
          createdAt: DateTime.parse('2024-01-01T00:00:00Z'),
        );

        // Assert
        expect(user1, equals(user2));
        expect(user1.hashCode, equals(user2.hashCode));
      });

      test('should not be equal when fields differ', () {
        // Arrange
        final user1 = WebUserModel(
          id: 'test-user-id-1',
          email: '<EMAIL>',
          name: 'Test User',
          createdAt: DateTime.parse('2024-01-01T00:00:00Z'),
        );

        final user2 = WebUserModel(
          id: 'test-user-id-2',
          email: '<EMAIL>',
          name: 'Test User',
          createdAt: DateTime.parse('2024-01-01T00:00:00Z'),
        );

        // Assert
        expect(user1, isNot(equals(user2)));
      });
    });

    group('validation', () {
      test('should validate email format', () {
        // Arrange
        final user = WebUserModel(
          id: 'test-user-id',
          email: '<EMAIL>',
          createdAt: DateTime.now(),
        );

        // Assert
        expect(user.isEmailValid, isTrue);
      });

      test('should detect invalid email format', () {
        // Arrange
        final user = WebUserModel(
          id: 'test-user-id',
          email: 'invalid-email',
          createdAt: DateTime.now(),
        );

        // Assert
        expect(user.isEmailValid, isFalse);
      });

      test('should check if email is confirmed', () {
        // Arrange
        final confirmedUser = WebUserModel(
          id: 'test-user-id',
          email: '<EMAIL>',
          createdAt: DateTime.now(),
          emailConfirmedAt: DateTime.now(),
        );

        final unconfirmedUser = WebUserModel(
          id: 'test-user-id',
          email: '<EMAIL>',
          createdAt: DateTime.now(),
        );

        // Assert
        expect(confirmedUser.isEmailConfirmed, isTrue);
        expect(unconfirmedUser.isEmailConfirmed, isFalse);
      });
    });
  });
}
