import 'package:flutter_test/flutter_test.dart';
import 'package:meditatingleo_webapp/features/auth/data/models/auth_session_model.dart';

void main() {
  group('AuthSessionModel', () {
    const testSessionData = {
      'access_token': 'test-access-token',
      'refresh_token': 'test-refresh-token',
      'expires_in': 3600,
      'expires_at': 1704067200, // 2024-01-01T00:00:00Z
      'token_type': 'bearer',
      'user_id': 'test-user-id',
    };

    group('fromJson', () {
      test('should create AuthSessionModel from valid JSON', () {
        // Act
        final session = AuthSessionModel.fromJson(testSessionData);

        // Assert
        expect(session.accessToken, 'test-access-token');
        expect(session.refreshToken, 'test-refresh-token');
        expect(session.expiresIn, 3600);
        expect(session.expiresAt, DateTime.fromMillisecondsSinceEpoch(1704067200 * 1000));
        expect(session.tokenType, 'bearer');
        expect(session.userId, 'test-user-id');
      });

      test('should handle null optional fields', () {
        // Arrange
        final minimalData = {
          'access_token': 'test-access-token',
          'expires_in': 3600,
          'user_id': 'test-user-id',
        };

        // Act
        final session = AuthSessionModel.fromJson(minimalData);

        // Assert
        expect(session.accessToken, 'test-access-token');
        expect(session.refreshToken, isNull);
        expect(session.expiresIn, 3600);
        expect(session.expiresAt, isNull);
        expect(session.tokenType, 'bearer'); // Default value
        expect(session.userId, 'test-user-id');
      });
    });

    group('toJson', () {
      test('should convert AuthSessionModel to JSON', () {
        // Arrange
        final session = AuthSessionModel(
          accessToken: 'test-access-token',
          refreshToken: 'test-refresh-token',
          expiresIn: 3600,
          expiresAt: DateTime.fromMillisecondsSinceEpoch(1704067200 * 1000),
          tokenType: 'bearer',
          userId: 'test-user-id',
        );

        // Act
        final json = session.toJson();

        // Assert
        expect(json['access_token'], 'test-access-token');
        expect(json['refresh_token'], 'test-refresh-token');
        expect(json['expires_in'], 3600);
        expect(json['expires_at'], 1704067200);
        expect(json['token_type'], 'bearer');
        expect(json['user_id'], 'test-user-id');
      });
    });

    group('validation', () {
      test('should detect expired session', () {
        // Arrange
        final expiredSession = AuthSessionModel(
          accessToken: 'test-access-token',
          expiresIn: 3600,
          expiresAt: DateTime.now().subtract(const Duration(hours: 1)),
          userId: 'test-user-id',
        );

        final validSession = AuthSessionModel(
          accessToken: 'test-access-token',
          expiresIn: 3600,
          expiresAt: DateTime.now().add(const Duration(hours: 1)),
          userId: 'test-user-id',
        );

        // Assert
        expect(expiredSession.isExpired, isTrue);
        expect(validSession.isExpired, isFalse);
      });

      test('should detect session expiring soon', () {
        // Arrange
        final expiringSoonSession = AuthSessionModel(
          accessToken: 'test-access-token',
          expiresIn: 3600,
          expiresAt: DateTime.now().add(const Duration(minutes: 2)),
          userId: 'test-user-id',
        );

        final validSession = AuthSessionModel(
          accessToken: 'test-access-token',
          expiresIn: 3600,
          expiresAt: DateTime.now().add(const Duration(hours: 1)),
          userId: 'test-user-id',
        );

        // Assert
        expect(expiringSoonSession.isExpiringSoon, isTrue);
        expect(validSession.isExpiringSoon, isFalse);
      });

      test('should check if session is valid', () {
        // Arrange
        final validSession = AuthSessionModel(
          accessToken: 'test-access-token',
          expiresIn: 3600,
          expiresAt: DateTime.now().add(const Duration(hours: 1)),
          userId: 'test-user-id',
        );

        final invalidSession = AuthSessionModel(
          accessToken: '',
          expiresIn: 3600,
          userId: 'test-user-id',
        );

        // Assert
        expect(validSession.isValid, isTrue);
        expect(invalidSession.isValid, isFalse);
      });

      test('should calculate remaining time', () {
        // Arrange
        final session = AuthSessionModel(
          accessToken: 'test-access-token',
          expiresIn: 3600,
          expiresAt: DateTime.now().add(const Duration(minutes: 30)),
          userId: 'test-user-id',
        );

        // Act
        final remainingTime = session.remainingTime;

        // Assert
        expect(remainingTime.inMinutes, closeTo(30, 1));
      });
    });

    group('copyWith', () {
      test('should create copy with updated fields', () {
        // Arrange
        final originalSession = AuthSessionModel(
          accessToken: 'test-access-token',
          refreshToken: 'test-refresh-token',
          expiresIn: 3600,
          userId: 'test-user-id',
        );

        // Act
        final updatedSession = originalSession.copyWith(
          accessToken: 'new-access-token',
          expiresIn: 7200,
        );

        // Assert
        expect(updatedSession.accessToken, 'new-access-token');
        expect(updatedSession.refreshToken, 'test-refresh-token');
        expect(updatedSession.expiresIn, 7200);
        expect(updatedSession.userId, 'test-user-id');
      });
    });
  });
}
