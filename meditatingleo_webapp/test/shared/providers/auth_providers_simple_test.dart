import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:meditatingleo_webapp/shared/providers/auth_providers.dart';

void main() {
  group('AuthNotifier', () {
    late ProviderContainer container;

    setUp(() {
      container = ProviderContainer();
    });

    tearDown(() {
      container.dispose();
    });

    test('should have initial state', () {
      // Act
      final state = container.read(authNotifierProvider);

      // Assert
      expect(state.hasValue, true);
      expect(state.value?.isAuthenticated, false);
      expect(state.value?.user, null);
    });

    test('should handle loading state', () {
      // Act
      final notifier = container.read(authNotifierProvider.notifier);
      
      // Initial state should not be loading
      var state = container.read(authNotifierProvider);
      expect(state.value?.isLoading, false);
    });
  });

  group('WebSessionNotifier', () {
    late ProviderContainer container;

    setUp(() {
      container = ProviderContainer();
    });

    tearDown(() {
      container.dispose();
    });

    test('should manage web session state', () {
      // Act
      final notifier = container.read(webSessionNotifierProvider.notifier);
      
      // Test initial state
      var state = container.read(webSessionNotifierProvider);
      expect(state.isRememberMeEnabled, false);
      expect(state.sessionTimeout, null);

      // Test enabling remember me
      notifier.setRememberMe(true);
      state = container.read(webSessionNotifierProvider);
      expect(state.isRememberMeEnabled, true);

      // Test setting session timeout
      final timeout = DateTime.now().add(Duration(hours: 24));
      notifier.setSessionTimeout(timeout);
      state = container.read(webSessionNotifierProvider);
      expect(state.sessionTimeout, timeout);

      // Test clearing session
      notifier.clearSession();
      state = container.read(webSessionNotifierProvider);
      expect(state.isRememberMeEnabled, false);
      expect(state.sessionTimeout, null);
    });

    test('should check session expiration', () {
      final notifier = container.read(webSessionNotifierProvider.notifier);

      // Test with no timeout
      expect(notifier.isSessionExpired(), false);

      // Test with future timeout
      final futureTimeout = DateTime.now().add(Duration(hours: 1));
      notifier.setSessionTimeout(futureTimeout);
      expect(notifier.isSessionExpired(), false);

      // Test with past timeout
      final pastTimeout = DateTime.now().subtract(Duration(hours: 1));
      notifier.setSessionTimeout(pastTimeout);
      expect(notifier.isSessionExpired(), true);
    });

    test('should extend session', () {
      final notifier = container.read(webSessionNotifierProvider.notifier);
      
      // Extend session
      notifier.extendSession(Duration(hours: 2));
      
      final state = container.read(webSessionNotifierProvider);
      expect(state.sessionTimeout, isNotNull);
      expect(state.sessionTimeout!.isAfter(DateTime.now()), true);
    });
  });
}
