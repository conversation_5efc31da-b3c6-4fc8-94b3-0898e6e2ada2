import 'dart:async';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';

import 'package:meditatingleo_webapp/features/database/data/services/supabase_service.dart';
import 'package:meditatingleo_webapp/core/services/web_cache_service.dart';
import 'package:meditatingleo_webapp/shared/models/result.dart';
import 'package:meditatingleo_webapp/shared/models/app_error.dart';

/// Helper class for testing Riverpod providers
class ProviderTestHelper {
  /// Creates a test container with common overrides
  static ProviderContainer createTestContainer({
    SupabaseService? mockSupabaseService,
    WebCacheService? mockCacheService,
    List<Override>? additionalOverrides,
  }) {
    final overrides = <Override>[];

    if (mockSupabaseService != null) {
      overrides.add(
        supabaseServiceProvider.overrideWithValue(mockSupabaseService),
      );
    }

    if (mockCacheService != null) {
      overrides.add(
        webCacheServiceProvider.overrideWithValue(mockCacheService),
      );
    }

    if (additionalOverrides != null) {
      overrides.addAll(additionalOverrides);
    }

    return ProviderContainer(overrides: overrides);
  }

  /// Waits for a provider to complete loading
  static Future<void> waitForProvider<T>(
    ProviderContainer container,
    ProviderListenable<AsyncValue<T>> provider, {
    Duration timeout = const Duration(seconds: 5),
  }) async {
    final completer = Completer<void>();
    late final ProviderSubscription subscription;

    subscription = container.listen(
      provider,
      (previous, next) {
        if (!next.isLoading && !completer.isCompleted) {
          completer.complete();
          subscription.close();
        }
      },
    );

    await completer.future.timeout(timeout);
  }

  /// Pumps the provider until it's no longer loading
  static Future<AsyncValue<T>> pumpProvider<T>(
    ProviderContainer container,
    ProviderListenable<AsyncValue<T>> provider,
  ) async {
    await waitForProvider(container, provider);
    return container.read(provider);
  }

  /// Creates a mock that returns a successful result
  static void mockSuccess<T>(
    Mock mock,
    String methodName,
    T result,
  ) {
    when(mock.noSuchMethod(
      Invocation.method(Symbol(methodName), []),
    )).thenAnswer((_) async => Result.success(result));
  }

  /// Creates a mock that returns a failure result
  static void mockFailure<T>(
    Mock mock,
    String methodName,
    AppError error,
  ) {
    when(mock.noSuchMethod(
      Invocation.method(Symbol(methodName), []),
    )).thenAnswer((_) async => Result.failure(error));
  }

  /// Verifies that a provider state matches expected values
  static void verifyProviderState<T>({
    required AsyncValue<T> state,
    bool? hasValue,
    bool? hasError,
    bool? isLoading,
    T? expectedValue,
    String? expectedErrorMessage,
  }) {
    if (hasValue != null) {
      expect(state.hasValue, hasValue, reason: 'hasValue mismatch');
    }

    if (hasError != null) {
      expect(state.hasError, hasError, reason: 'hasError mismatch');
    }

    if (isLoading != null) {
      expect(state.isLoading, isLoading, reason: 'isLoading mismatch');
    }

    if (expectedValue != null) {
      expect(state.value, expectedValue, reason: 'value mismatch');
    }

    if (expectedErrorMessage != null) {
      expect(
        state.error.toString(),
        contains(expectedErrorMessage),
        reason: 'error message mismatch',
      );
    }
  }

  /// Creates a stream that emits values with delays
  static Stream<T> createDelayedStream<T>(
    List<T> values, {
    Duration delay = const Duration(milliseconds: 100),
  }) async* {
    for (final value in values) {
      await Future.delayed(delay);
      yield value;
    }
  }

  /// Disposes a container safely
  static void disposeContainer(ProviderContainer container) {
    try {
      container.dispose();
    } catch (e) {
      // Ignore disposal errors in tests
    }
  }
}

/// Extension methods for easier testing
extension ProviderContainerTestExtensions on ProviderContainer {
  /// Reads a provider and expects it to have a value
  T readValue<T>(ProviderListenable<AsyncValue<T>> provider) {
    final state = read(provider);
    expect(state.hasValue, true, reason: 'Provider should have a value');
    return state.value!;
  }

  /// Reads a provider and expects it to have an error
  Object readError<T>(ProviderListenable<AsyncValue<T>> provider) {
    final state = read(provider);
    expect(state.hasError, true, reason: 'Provider should have an error');
    return state.error!;
  }

  /// Reads a provider and expects it to be loading
  void expectLoading<T>(ProviderListenable<AsyncValue<T>> provider) {
    final state = read(provider);
    expect(state.isLoading, true, reason: 'Provider should be loading');
  }
}

/// Mock data factory for tests
class MockDataFactory {
  static Map<String, dynamic> createJournalEntry({
    String? id,
    String? title,
    String? content,
    String? userId,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    final now = DateTime.now();
    return {
      'id': id ?? 'test_id',
      'title': title ?? 'Test Title',
      'content': content ?? 'Test content',
      'user_id': userId ?? 'test_user',
      'created_at': (createdAt ?? now).toIso8601String(),
      'updated_at': (updatedAt ?? now).toIso8601String(),
    };
  }

  static Map<String, dynamic> createUser({
    String? id,
    String? email,
    String? name,
    DateTime? createdAt,
  }) {
    return {
      'id': id ?? 'test_user_id',
      'email': email ?? '<EMAIL>',
      'name': name ?? 'Test User',
      'created_at': (createdAt ?? DateTime.now()).toIso8601String(),
    };
  }

  static Map<String, dynamic> createAuthSession({
    String? userId,
    String? accessToken,
    String? refreshToken,
    DateTime? expiresAt,
  }) {
    return {
      'user': createUser(id: userId),
      'access_token': accessToken ?? 'test_access_token',
      'refresh_token': refreshToken ?? 'test_refresh_token',
      'expires_at': (expiresAt ?? DateTime.now().add(const Duration(hours: 1)))
          .toIso8601String(),
    };
  }

  static Map<String, dynamic> createCacheEntry({
    String? key,
    dynamic value,
    DateTime? expiresAt,
  }) {
    return {
      'key': key ?? 'test_key',
      'value': value ?? {'test': 'data'},
      'expires_at': expiresAt?.toIso8601String(),
    };
  }
}

/// Test matchers for common assertions
class TestMatchers {
  /// Matches an AsyncValue that has a value
  static Matcher hasAsyncValue<T>([T? expectedValue]) {
    return predicate<AsyncValue<T>>((state) {
      if (!state.hasValue) return false;
      if (expectedValue != null) {
        return state.value == expectedValue;
      }
      return true;
    }, 'has async value${expectedValue != null ? ' $expectedValue' : ''}');
  }

  /// Matches an AsyncValue that has an error
  static Matcher hasAsyncError([String? expectedMessage]) {
    return predicate<AsyncValue>((state) {
      if (!state.hasError) return false;
      if (expectedMessage != null) {
        return state.error.toString().contains(expectedMessage);
      }
      return true;
    }, 'has async error${expectedMessage != null ? ' containing "$expectedMessage"' : ''}');
  }

  /// Matches an AsyncValue that is loading
  static Matcher get isAsyncLoading {
    return predicate<AsyncValue>(
        (state) => state.isLoading, 'is async loading');
  }

  /// Matches a list with specific length
  static Matcher hasLength(int expectedLength) {
    return predicate<List>(
        (list) => list.length == expectedLength, 'has length $expectedLength');
  }

  /// Matches a map containing specific key-value pairs
  static Matcher containsKeyValue(String key, dynamic value) {
    return predicate<Map>((map) => map[key] == value, 'contains $key: $value');
  }
}
