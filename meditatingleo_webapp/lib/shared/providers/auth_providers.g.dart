// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'auth_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$authStreamHash() => r'a730e70928e4c3bfd72e869206db4c52922da8ac';

/// Authentication stream provider for real-time auth state
///
/// Copied from [authStream].
@ProviderFor(authStream)
final authStreamProvider = AutoDisposeStreamProvider<AuthState>.internal(
  authStream,
  name: r'authStreamProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$authStreamHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AuthStreamRef = AutoDisposeStreamProviderRef<AuthState>;
String _$authNotifierHash() => r'64e38155c4e2eb980ea3edeaefcec1720ea6efc9';

/// Authentication state notifier
///
/// Copied from [AuthNotifier].
@ProviderFor(AuthNotifier)
final authNotifierProvider =
    AutoDisposeNotifierProvider<AuthNotifier, AsyncValue<AuthState>>.internal(
  AuthNotifier.new,
  name: r'authNotifierProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$authNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$AuthNotifier = AutoDisposeNotifier<AsyncValue<AuthState>>;
String _$webSessionNotifierHash() =>
    r'7f9b21bf08bc3fbfad570f45af48208f990588a6';

/// Web session state notifier
///
/// Copied from [WebSessionNotifier].
@ProviderFor(WebSessionNotifier)
final webSessionNotifierProvider =
    AutoDisposeNotifierProvider<WebSessionNotifier, WebSessionState>.internal(
  WebSessionNotifier.new,
  name: r'webSessionNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$webSessionNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$WebSessionNotifier = AutoDisposeNotifier<WebSessionState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
