import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../features/database/data/services/supabase_service.dart';
import '../models/result.dart';

part 'auth_providers.freezed.dart';
part 'auth_providers.g.dart';

/// Authentication state notifier
@riverpod
class AuthNotifier extends _$AuthNotifier {
  @override
  AsyncValue<AuthState> build() {
    return const AsyncValue.data(AuthState());
  }

  /// Sign in with email and password
  Future<void> signIn(String email, String password) async {
    state = const AsyncValue.loading();
    
    try {
      final supabaseService = ref.read(supabaseServiceProvider);
      final result = await supabaseService.signInWithEmail(email, password);
      
      result.when(
        success: (session) {
          state = AsyncValue.data(
            AuthState(
              isAuthenticated: true,
              user: session.user,
              isLoading: false,
            ),
          );
        },
        failure: (error) {
          state = AsyncValue.error(error, StackTrace.current);
        },
      );
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Sign up with email and password
  Future<void> signUp(String email, String password) async {
    state = const AsyncValue.loading();
    
    try {
      final supabaseService = ref.read(supabaseServiceProvider);
      final result = await supabaseService.signUpWithEmail(email, password);
      
      result.when(
        success: (session) {
          state = AsyncValue.data(
            AuthState(
              isAuthenticated: true,
              user: session.user,
              isLoading: false,
            ),
          );
        },
        failure: (error) {
          state = AsyncValue.error(error, StackTrace.current);
        },
      );
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Sign out the current user
  Future<void> signOut() async {
    state = const AsyncValue.loading();
    
    try {
      final supabaseService = ref.read(supabaseServiceProvider);
      final result = await supabaseService.signOut();
      
      result.when(
        success: (_) {
          state = const AsyncValue.data(
            AuthState(
              isAuthenticated: false,
              user: null,
              isLoading: false,
            ),
          );
        },
        failure: (error) {
          state = AsyncValue.error(error, StackTrace.current);
        },
      );
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Get the current user
  Future<void> getCurrentUser() async {
    state = const AsyncValue.loading();
    
    try {
      final supabaseService = ref.read(supabaseServiceProvider);
      final result = await supabaseService.getCurrentUser();
      
      result.when(
        success: (user) {
          state = AsyncValue.data(
            AuthState(
              isAuthenticated: user != null,
              user: user,
              isLoading: false,
            ),
          );
        },
        failure: (error) {
          state = AsyncValue.error(error, StackTrace.current);
        },
      );
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

/// Authentication stream provider for real-time auth state
@riverpod
Stream<AuthState> authStream(AuthStreamRef ref) {
  final supabaseService = ref.watch(supabaseServiceProvider);
  return supabaseService.authStateStream();
}

/// Web session state notifier
@riverpod
class WebSessionNotifier extends _$WebSessionNotifier {
  @override
  WebSessionState build() {
    return const WebSessionState();
  }

  /// Set remember me preference
  void setRememberMe(bool enabled) {
    state = state.copyWith(isRememberMeEnabled: enabled);
  }

  /// Set session timeout
  void setSessionTimeout(DateTime timeout) {
    state = state.copyWith(sessionTimeout: timeout);
  }

  /// Clear session data
  void clearSession() {
    state = const WebSessionState();
  }

  /// Check if session is expired
  bool isSessionExpired() {
    final timeout = state.sessionTimeout;
    if (timeout == null) return false;
    return DateTime.now().isAfter(timeout);
  }

  /// Extend session timeout
  void extendSession(Duration extension) {
    final newTimeout = DateTime.now().add(extension);
    state = state.copyWith(sessionTimeout: newTimeout);
  }
}

/// Authentication state model
@freezed
class AuthState with _$AuthState {
  const factory AuthState({
    @Default(false) bool isAuthenticated,
    User? user,
    @Default(false) bool isLoading,
    String? errorMessage,
  }) = _AuthState;
}

/// Web session state model
@freezed
class WebSessionState with _$WebSessionState {
  const factory WebSessionState({
    @Default(false) bool isRememberMeEnabled,
    DateTime? sessionTimeout,
    @Default(false) bool isSessionActive,
    String? sessionId,
  }) = _WebSessionState;
}
