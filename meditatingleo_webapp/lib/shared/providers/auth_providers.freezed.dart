// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'auth_providers.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$AuthState {
  bool get isAuthenticated => throw _privateConstructorUsedError;
  User? get user => throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;
  String? get errorMessage => throw _privateConstructorUsedError;

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AuthStateCopyWith<AuthState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AuthStateCopyWith<$Res> {
  factory $AuthStateCopyWith(AuthState value, $Res Function(AuthState) then) =
      _$AuthStateCopyWithImpl<$Res, AuthState>;
  @useResult
  $Res call(
      {bool isAuthenticated, User? user, bool isLoading, String? errorMessage});
}

/// @nodoc
class _$AuthStateCopyWithImpl<$Res, $Val extends AuthState>
    implements $AuthStateCopyWith<$Res> {
  _$AuthStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isAuthenticated = null,
    Object? user = freezed,
    Object? isLoading = null,
    Object? errorMessage = freezed,
  }) {
    return _then(_value.copyWith(
      isAuthenticated: null == isAuthenticated
          ? _value.isAuthenticated
          : isAuthenticated // ignore: cast_nullable_to_non_nullable
              as bool,
      user: freezed == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as User?,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AuthStateImplCopyWith<$Res>
    implements $AuthStateCopyWith<$Res> {
  factory _$$AuthStateImplCopyWith(
          _$AuthStateImpl value, $Res Function(_$AuthStateImpl) then) =
      __$$AuthStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool isAuthenticated, User? user, bool isLoading, String? errorMessage});
}

/// @nodoc
class __$$AuthStateImplCopyWithImpl<$Res>
    extends _$AuthStateCopyWithImpl<$Res, _$AuthStateImpl>
    implements _$$AuthStateImplCopyWith<$Res> {
  __$$AuthStateImplCopyWithImpl(
      _$AuthStateImpl _value, $Res Function(_$AuthStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isAuthenticated = null,
    Object? user = freezed,
    Object? isLoading = null,
    Object? errorMessage = freezed,
  }) {
    return _then(_$AuthStateImpl(
      isAuthenticated: null == isAuthenticated
          ? _value.isAuthenticated
          : isAuthenticated // ignore: cast_nullable_to_non_nullable
              as bool,
      user: freezed == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as User?,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$AuthStateImpl implements _AuthState {
  const _$AuthStateImpl(
      {this.isAuthenticated = false,
      this.user,
      this.isLoading = false,
      this.errorMessage});

  @override
  @JsonKey()
  final bool isAuthenticated;
  @override
  final User? user;
  @override
  @JsonKey()
  final bool isLoading;
  @override
  final String? errorMessage;

  @override
  String toString() {
    return 'AuthState(isAuthenticated: $isAuthenticated, user: $user, isLoading: $isLoading, errorMessage: $errorMessage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AuthStateImpl &&
            (identical(other.isAuthenticated, isAuthenticated) ||
                other.isAuthenticated == isAuthenticated) &&
            (identical(other.user, user) || other.user == user) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, isAuthenticated, user, isLoading, errorMessage);

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AuthStateImplCopyWith<_$AuthStateImpl> get copyWith =>
      __$$AuthStateImplCopyWithImpl<_$AuthStateImpl>(this, _$identity);
}

abstract class _AuthState implements AuthState {
  const factory _AuthState(
      {final bool isAuthenticated,
      final User? user,
      final bool isLoading,
      final String? errorMessage}) = _$AuthStateImpl;

  @override
  bool get isAuthenticated;
  @override
  User? get user;
  @override
  bool get isLoading;
  @override
  String? get errorMessage;

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AuthStateImplCopyWith<_$AuthStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$WebSessionState {
  bool get isRememberMeEnabled => throw _privateConstructorUsedError;
  DateTime? get sessionTimeout => throw _privateConstructorUsedError;
  bool get isSessionActive => throw _privateConstructorUsedError;
  String? get sessionId => throw _privateConstructorUsedError;

  /// Create a copy of WebSessionState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $WebSessionStateCopyWith<WebSessionState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WebSessionStateCopyWith<$Res> {
  factory $WebSessionStateCopyWith(
          WebSessionState value, $Res Function(WebSessionState) then) =
      _$WebSessionStateCopyWithImpl<$Res, WebSessionState>;
  @useResult
  $Res call(
      {bool isRememberMeEnabled,
      DateTime? sessionTimeout,
      bool isSessionActive,
      String? sessionId});
}

/// @nodoc
class _$WebSessionStateCopyWithImpl<$Res, $Val extends WebSessionState>
    implements $WebSessionStateCopyWith<$Res> {
  _$WebSessionStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of WebSessionState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isRememberMeEnabled = null,
    Object? sessionTimeout = freezed,
    Object? isSessionActive = null,
    Object? sessionId = freezed,
  }) {
    return _then(_value.copyWith(
      isRememberMeEnabled: null == isRememberMeEnabled
          ? _value.isRememberMeEnabled
          : isRememberMeEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      sessionTimeout: freezed == sessionTimeout
          ? _value.sessionTimeout
          : sessionTimeout // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      isSessionActive: null == isSessionActive
          ? _value.isSessionActive
          : isSessionActive // ignore: cast_nullable_to_non_nullable
              as bool,
      sessionId: freezed == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$WebSessionStateImplCopyWith<$Res>
    implements $WebSessionStateCopyWith<$Res> {
  factory _$$WebSessionStateImplCopyWith(_$WebSessionStateImpl value,
          $Res Function(_$WebSessionStateImpl) then) =
      __$$WebSessionStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool isRememberMeEnabled,
      DateTime? sessionTimeout,
      bool isSessionActive,
      String? sessionId});
}

/// @nodoc
class __$$WebSessionStateImplCopyWithImpl<$Res>
    extends _$WebSessionStateCopyWithImpl<$Res, _$WebSessionStateImpl>
    implements _$$WebSessionStateImplCopyWith<$Res> {
  __$$WebSessionStateImplCopyWithImpl(
      _$WebSessionStateImpl _value, $Res Function(_$WebSessionStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of WebSessionState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isRememberMeEnabled = null,
    Object? sessionTimeout = freezed,
    Object? isSessionActive = null,
    Object? sessionId = freezed,
  }) {
    return _then(_$WebSessionStateImpl(
      isRememberMeEnabled: null == isRememberMeEnabled
          ? _value.isRememberMeEnabled
          : isRememberMeEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      sessionTimeout: freezed == sessionTimeout
          ? _value.sessionTimeout
          : sessionTimeout // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      isSessionActive: null == isSessionActive
          ? _value.isSessionActive
          : isSessionActive // ignore: cast_nullable_to_non_nullable
              as bool,
      sessionId: freezed == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$WebSessionStateImpl implements _WebSessionState {
  const _$WebSessionStateImpl(
      {this.isRememberMeEnabled = false,
      this.sessionTimeout,
      this.isSessionActive = false,
      this.sessionId});

  @override
  @JsonKey()
  final bool isRememberMeEnabled;
  @override
  final DateTime? sessionTimeout;
  @override
  @JsonKey()
  final bool isSessionActive;
  @override
  final String? sessionId;

  @override
  String toString() {
    return 'WebSessionState(isRememberMeEnabled: $isRememberMeEnabled, sessionTimeout: $sessionTimeout, isSessionActive: $isSessionActive, sessionId: $sessionId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WebSessionStateImpl &&
            (identical(other.isRememberMeEnabled, isRememberMeEnabled) ||
                other.isRememberMeEnabled == isRememberMeEnabled) &&
            (identical(other.sessionTimeout, sessionTimeout) ||
                other.sessionTimeout == sessionTimeout) &&
            (identical(other.isSessionActive, isSessionActive) ||
                other.isSessionActive == isSessionActive) &&
            (identical(other.sessionId, sessionId) ||
                other.sessionId == sessionId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isRememberMeEnabled,
      sessionTimeout, isSessionActive, sessionId);

  /// Create a copy of WebSessionState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$WebSessionStateImplCopyWith<_$WebSessionStateImpl> get copyWith =>
      __$$WebSessionStateImplCopyWithImpl<_$WebSessionStateImpl>(
          this, _$identity);
}

abstract class _WebSessionState implements WebSessionState {
  const factory _WebSessionState(
      {final bool isRememberMeEnabled,
      final DateTime? sessionTimeout,
      final bool isSessionActive,
      final String? sessionId}) = _$WebSessionStateImpl;

  @override
  bool get isRememberMeEnabled;
  @override
  DateTime? get sessionTimeout;
  @override
  bool get isSessionActive;
  @override
  String? get sessionId;

  /// Create a copy of WebSessionState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$WebSessionStateImplCopyWith<_$WebSessionStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
