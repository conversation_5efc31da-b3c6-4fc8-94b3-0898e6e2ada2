// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'cache_providers.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$WebCacheState {
  Set<String> get cachedKeys => throw _privateConstructorUsedError;
  int get cacheSize => throw _privateConstructorUsedError;
  DateTime? get lastCleared => throw _privateConstructorUsedError;

  /// Create a copy of WebCacheState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $WebCacheStateCopyWith<WebCacheState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WebCacheStateCopyWith<$Res> {
  factory $WebCacheStateCopyWith(
          WebCacheState value, $Res Function(WebCacheState) then) =
      _$WebCacheStateCopyWithImpl<$Res, WebCacheState>;
  @useResult
  $Res call({Set<String> cachedKeys, int cacheSize, DateTime? lastCleared});
}

/// @nodoc
class _$WebCacheStateCopyWithImpl<$Res, $Val extends WebCacheState>
    implements $WebCacheStateCopyWith<$Res> {
  _$WebCacheStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of WebCacheState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? cachedKeys = null,
    Object? cacheSize = null,
    Object? lastCleared = freezed,
  }) {
    return _then(_value.copyWith(
      cachedKeys: null == cachedKeys
          ? _value.cachedKeys
          : cachedKeys // ignore: cast_nullable_to_non_nullable
              as Set<String>,
      cacheSize: null == cacheSize
          ? _value.cacheSize
          : cacheSize // ignore: cast_nullable_to_non_nullable
              as int,
      lastCleared: freezed == lastCleared
          ? _value.lastCleared
          : lastCleared // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$WebCacheStateImplCopyWith<$Res>
    implements $WebCacheStateCopyWith<$Res> {
  factory _$$WebCacheStateImplCopyWith(
          _$WebCacheStateImpl value, $Res Function(_$WebCacheStateImpl) then) =
      __$$WebCacheStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({Set<String> cachedKeys, int cacheSize, DateTime? lastCleared});
}

/// @nodoc
class __$$WebCacheStateImplCopyWithImpl<$Res>
    extends _$WebCacheStateCopyWithImpl<$Res, _$WebCacheStateImpl>
    implements _$$WebCacheStateImplCopyWith<$Res> {
  __$$WebCacheStateImplCopyWithImpl(
      _$WebCacheStateImpl _value, $Res Function(_$WebCacheStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of WebCacheState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? cachedKeys = null,
    Object? cacheSize = null,
    Object? lastCleared = freezed,
  }) {
    return _then(_$WebCacheStateImpl(
      cachedKeys: null == cachedKeys
          ? _value._cachedKeys
          : cachedKeys // ignore: cast_nullable_to_non_nullable
              as Set<String>,
      cacheSize: null == cacheSize
          ? _value.cacheSize
          : cacheSize // ignore: cast_nullable_to_non_nullable
              as int,
      lastCleared: freezed == lastCleared
          ? _value.lastCleared
          : lastCleared // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc

class _$WebCacheStateImpl implements _WebCacheState {
  const _$WebCacheStateImpl(
      {final Set<String> cachedKeys = const {},
      this.cacheSize = 0,
      this.lastCleared})
      : _cachedKeys = cachedKeys;

  final Set<String> _cachedKeys;
  @override
  @JsonKey()
  Set<String> get cachedKeys {
    if (_cachedKeys is EqualUnmodifiableSetView) return _cachedKeys;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableSetView(_cachedKeys);
  }

  @override
  @JsonKey()
  final int cacheSize;
  @override
  final DateTime? lastCleared;

  @override
  String toString() {
    return 'WebCacheState(cachedKeys: $cachedKeys, cacheSize: $cacheSize, lastCleared: $lastCleared)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WebCacheStateImpl &&
            const DeepCollectionEquality()
                .equals(other._cachedKeys, _cachedKeys) &&
            (identical(other.cacheSize, cacheSize) ||
                other.cacheSize == cacheSize) &&
            (identical(other.lastCleared, lastCleared) ||
                other.lastCleared == lastCleared));
  }

  @override
  int get hashCode => Object.hash(runtimeType,
      const DeepCollectionEquality().hash(_cachedKeys), cacheSize, lastCleared);

  /// Create a copy of WebCacheState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$WebCacheStateImplCopyWith<_$WebCacheStateImpl> get copyWith =>
      __$$WebCacheStateImplCopyWithImpl<_$WebCacheStateImpl>(this, _$identity);
}

abstract class _WebCacheState implements WebCacheState {
  const factory _WebCacheState(
      {final Set<String> cachedKeys,
      final int cacheSize,
      final DateTime? lastCleared}) = _$WebCacheStateImpl;

  @override
  Set<String> get cachedKeys;
  @override
  int get cacheSize;
  @override
  DateTime? get lastCleared;

  /// Create a copy of WebCacheState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$WebCacheStateImplCopyWith<_$WebCacheStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$SessionCacheState {
  Set<String> get sessionKeys => throw _privateConstructorUsedError;
  int get sessionSize => throw _privateConstructorUsedError;
  DateTime? get lastCleared => throw _privateConstructorUsedError;

  /// Create a copy of SessionCacheState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SessionCacheStateCopyWith<SessionCacheState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SessionCacheStateCopyWith<$Res> {
  factory $SessionCacheStateCopyWith(
          SessionCacheState value, $Res Function(SessionCacheState) then) =
      _$SessionCacheStateCopyWithImpl<$Res, SessionCacheState>;
  @useResult
  $Res call({Set<String> sessionKeys, int sessionSize, DateTime? lastCleared});
}

/// @nodoc
class _$SessionCacheStateCopyWithImpl<$Res, $Val extends SessionCacheState>
    implements $SessionCacheStateCopyWith<$Res> {
  _$SessionCacheStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SessionCacheState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? sessionKeys = null,
    Object? sessionSize = null,
    Object? lastCleared = freezed,
  }) {
    return _then(_value.copyWith(
      sessionKeys: null == sessionKeys
          ? _value.sessionKeys
          : sessionKeys // ignore: cast_nullable_to_non_nullable
              as Set<String>,
      sessionSize: null == sessionSize
          ? _value.sessionSize
          : sessionSize // ignore: cast_nullable_to_non_nullable
              as int,
      lastCleared: freezed == lastCleared
          ? _value.lastCleared
          : lastCleared // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SessionCacheStateImplCopyWith<$Res>
    implements $SessionCacheStateCopyWith<$Res> {
  factory _$$SessionCacheStateImplCopyWith(_$SessionCacheStateImpl value,
          $Res Function(_$SessionCacheStateImpl) then) =
      __$$SessionCacheStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({Set<String> sessionKeys, int sessionSize, DateTime? lastCleared});
}

/// @nodoc
class __$$SessionCacheStateImplCopyWithImpl<$Res>
    extends _$SessionCacheStateCopyWithImpl<$Res, _$SessionCacheStateImpl>
    implements _$$SessionCacheStateImplCopyWith<$Res> {
  __$$SessionCacheStateImplCopyWithImpl(_$SessionCacheStateImpl _value,
      $Res Function(_$SessionCacheStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of SessionCacheState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? sessionKeys = null,
    Object? sessionSize = null,
    Object? lastCleared = freezed,
  }) {
    return _then(_$SessionCacheStateImpl(
      sessionKeys: null == sessionKeys
          ? _value._sessionKeys
          : sessionKeys // ignore: cast_nullable_to_non_nullable
              as Set<String>,
      sessionSize: null == sessionSize
          ? _value.sessionSize
          : sessionSize // ignore: cast_nullable_to_non_nullable
              as int,
      lastCleared: freezed == lastCleared
          ? _value.lastCleared
          : lastCleared // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc

class _$SessionCacheStateImpl implements _SessionCacheState {
  const _$SessionCacheStateImpl(
      {final Set<String> sessionKeys = const {},
      this.sessionSize = 0,
      this.lastCleared})
      : _sessionKeys = sessionKeys;

  final Set<String> _sessionKeys;
  @override
  @JsonKey()
  Set<String> get sessionKeys {
    if (_sessionKeys is EqualUnmodifiableSetView) return _sessionKeys;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableSetView(_sessionKeys);
  }

  @override
  @JsonKey()
  final int sessionSize;
  @override
  final DateTime? lastCleared;

  @override
  String toString() {
    return 'SessionCacheState(sessionKeys: $sessionKeys, sessionSize: $sessionSize, lastCleared: $lastCleared)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SessionCacheStateImpl &&
            const DeepCollectionEquality()
                .equals(other._sessionKeys, _sessionKeys) &&
            (identical(other.sessionSize, sessionSize) ||
                other.sessionSize == sessionSize) &&
            (identical(other.lastCleared, lastCleared) ||
                other.lastCleared == lastCleared));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_sessionKeys),
      sessionSize,
      lastCleared);

  /// Create a copy of SessionCacheState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SessionCacheStateImplCopyWith<_$SessionCacheStateImpl> get copyWith =>
      __$$SessionCacheStateImplCopyWithImpl<_$SessionCacheStateImpl>(
          this, _$identity);
}

abstract class _SessionCacheState implements SessionCacheState {
  const factory _SessionCacheState(
      {final Set<String> sessionKeys,
      final int sessionSize,
      final DateTime? lastCleared}) = _$SessionCacheStateImpl;

  @override
  Set<String> get sessionKeys;
  @override
  int get sessionSize;
  @override
  DateTime? get lastCleared;

  /// Create a copy of SessionCacheState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SessionCacheStateImplCopyWith<_$SessionCacheStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$LocalStorageState {
  Set<String> get localKeys => throw _privateConstructorUsedError;
  int get localSize => throw _privateConstructorUsedError;
  DateTime? get lastCleared => throw _privateConstructorUsedError;

  /// Create a copy of LocalStorageState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $LocalStorageStateCopyWith<LocalStorageState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LocalStorageStateCopyWith<$Res> {
  factory $LocalStorageStateCopyWith(
          LocalStorageState value, $Res Function(LocalStorageState) then) =
      _$LocalStorageStateCopyWithImpl<$Res, LocalStorageState>;
  @useResult
  $Res call({Set<String> localKeys, int localSize, DateTime? lastCleared});
}

/// @nodoc
class _$LocalStorageStateCopyWithImpl<$Res, $Val extends LocalStorageState>
    implements $LocalStorageStateCopyWith<$Res> {
  _$LocalStorageStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of LocalStorageState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? localKeys = null,
    Object? localSize = null,
    Object? lastCleared = freezed,
  }) {
    return _then(_value.copyWith(
      localKeys: null == localKeys
          ? _value.localKeys
          : localKeys // ignore: cast_nullable_to_non_nullable
              as Set<String>,
      localSize: null == localSize
          ? _value.localSize
          : localSize // ignore: cast_nullable_to_non_nullable
              as int,
      lastCleared: freezed == lastCleared
          ? _value.lastCleared
          : lastCleared // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$LocalStorageStateImplCopyWith<$Res>
    implements $LocalStorageStateCopyWith<$Res> {
  factory _$$LocalStorageStateImplCopyWith(_$LocalStorageStateImpl value,
          $Res Function(_$LocalStorageStateImpl) then) =
      __$$LocalStorageStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({Set<String> localKeys, int localSize, DateTime? lastCleared});
}

/// @nodoc
class __$$LocalStorageStateImplCopyWithImpl<$Res>
    extends _$LocalStorageStateCopyWithImpl<$Res, _$LocalStorageStateImpl>
    implements _$$LocalStorageStateImplCopyWith<$Res> {
  __$$LocalStorageStateImplCopyWithImpl(_$LocalStorageStateImpl _value,
      $Res Function(_$LocalStorageStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of LocalStorageState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? localKeys = null,
    Object? localSize = null,
    Object? lastCleared = freezed,
  }) {
    return _then(_$LocalStorageStateImpl(
      localKeys: null == localKeys
          ? _value._localKeys
          : localKeys // ignore: cast_nullable_to_non_nullable
              as Set<String>,
      localSize: null == localSize
          ? _value.localSize
          : localSize // ignore: cast_nullable_to_non_nullable
              as int,
      lastCleared: freezed == lastCleared
          ? _value.lastCleared
          : lastCleared // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc

class _$LocalStorageStateImpl implements _LocalStorageState {
  const _$LocalStorageStateImpl(
      {final Set<String> localKeys = const {},
      this.localSize = 0,
      this.lastCleared})
      : _localKeys = localKeys;

  final Set<String> _localKeys;
  @override
  @JsonKey()
  Set<String> get localKeys {
    if (_localKeys is EqualUnmodifiableSetView) return _localKeys;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableSetView(_localKeys);
  }

  @override
  @JsonKey()
  final int localSize;
  @override
  final DateTime? lastCleared;

  @override
  String toString() {
    return 'LocalStorageState(localKeys: $localKeys, localSize: $localSize, lastCleared: $lastCleared)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LocalStorageStateImpl &&
            const DeepCollectionEquality()
                .equals(other._localKeys, _localKeys) &&
            (identical(other.localSize, localSize) ||
                other.localSize == localSize) &&
            (identical(other.lastCleared, lastCleared) ||
                other.lastCleared == lastCleared));
  }

  @override
  int get hashCode => Object.hash(runtimeType,
      const DeepCollectionEquality().hash(_localKeys), localSize, lastCleared);

  /// Create a copy of LocalStorageState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LocalStorageStateImplCopyWith<_$LocalStorageStateImpl> get copyWith =>
      __$$LocalStorageStateImplCopyWithImpl<_$LocalStorageStateImpl>(
          this, _$identity);
}

abstract class _LocalStorageState implements LocalStorageState {
  const factory _LocalStorageState(
      {final Set<String> localKeys,
      final int localSize,
      final DateTime? lastCleared}) = _$LocalStorageStateImpl;

  @override
  Set<String> get localKeys;
  @override
  int get localSize;
  @override
  DateTime? get lastCleared;

  /// Create a copy of LocalStorageState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LocalStorageStateImplCopyWith<_$LocalStorageStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
