// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'cache_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$webCacheNotifierHash() => r'd7e16cf44a96f0864682351853a74351ccdbfbda';

/// Web cache state notifier
///
/// Copied from [WebCacheNotifier].
@ProviderFor(WebCacheNotifier)
final webCacheNotifierProvider = AutoDisposeNotifierProvider<WebCacheNotifier,
    AsyncValue<WebCacheState>>.internal(
  WebCacheNotifier.new,
  name: r'webCacheNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$webCacheNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$WebCacheNotifier = AutoDisposeNotifier<AsyncValue<WebCacheState>>;
String _$sessionCacheNotifierHash() =>
    r'e43b35dffc4215b0b5531c9c0998835400fb218e';

/// Session cache state notifier
///
/// Copied from [SessionCacheNotifier].
@ProviderFor(SessionCacheNotifier)
final sessionCacheNotifierProvider = AutoDisposeNotifierProvider<
    SessionCacheNotifier, AsyncValue<SessionCacheState>>.internal(
  SessionCacheNotifier.new,
  name: r'sessionCacheNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$sessionCacheNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SessionCacheNotifier
    = AutoDisposeNotifier<AsyncValue<SessionCacheState>>;
String _$localStorageNotifierHash() =>
    r'4c0c1d021250117f859bdbefa257cb5fbe1843ea';

/// Local storage state notifier
///
/// Copied from [LocalStorageNotifier].
@ProviderFor(LocalStorageNotifier)
final localStorageNotifierProvider = AutoDisposeNotifierProvider<
    LocalStorageNotifier, AsyncValue<LocalStorageState>>.internal(
  LocalStorageNotifier.new,
  name: r'localStorageNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$localStorageNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$LocalStorageNotifier
    = AutoDisposeNotifier<AsyncValue<LocalStorageState>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
