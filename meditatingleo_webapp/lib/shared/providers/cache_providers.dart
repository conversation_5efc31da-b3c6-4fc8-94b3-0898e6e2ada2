import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../../core/services/web_cache_service.dart';

part 'cache_providers.freezed.dart';
part 'cache_providers.g.dart';

/// Web cache state notifier
@riverpod
class WebCacheNotifier extends _$WebCacheNotifier {
  @override
  AsyncValue<WebCacheState> build() {
    return const AsyncValue.data(WebCacheState());
  }

  /// Cache data with a key
  Future<void> cacheData(String key, dynamic data) async {
    state = const AsyncValue.loading();
    
    try {
      final cacheService = ref.read(webCacheServiceProvider);
      await cacheService.set(key, data);
      
      final currentState = state.value ?? const WebCacheState();
      final updatedKeys = Set<String>.from(currentState.cachedKeys)..add(key);
      
      state = AsyncValue.data(
        currentState.copyWith(cachedKeys: updatedKeys),
      );
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Get cached data by key
  Future<dynamic> getCachedData(String key) async {
    try {
      final cacheService = ref.read(webCacheServiceProvider);
      return await cacheService.get(key);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
      return null;
    }
  }

  /// Clear all cached data
  Future<void> clearCache() async {
    state = const AsyncValue.loading();
    
    try {
      final cacheService = ref.read(webCacheServiceProvider);
      await cacheService.clear();
      
      state = const AsyncValue.data(WebCacheState());
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Remove specific cached data
  Future<void> removeCachedData(String key) async {
    try {
      final cacheService = ref.read(webCacheServiceProvider);
      await cacheService.remove(key);
      
      final currentState = state.value ?? const WebCacheState();
      final updatedKeys = Set<String>.from(currentState.cachedKeys)..remove(key);
      
      state = AsyncValue.data(
        currentState.copyWith(cachedKeys: updatedKeys),
      );
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Get cache size
  int getCacheSize() {
    final cacheService = ref.read(webCacheServiceProvider);
    return cacheService.getCacheSize();
  }

  /// Check if key exists in cache
  bool hasKey(String key) {
    final cacheService = ref.read(webCacheServiceProvider);
    return cacheService.hasKey(key);
  }
}

/// Session cache state notifier
@riverpod
class SessionCacheNotifier extends _$SessionCacheNotifier {
  @override
  AsyncValue<SessionCacheState> build() {
    return const AsyncValue.data(SessionCacheState());
  }

  /// Cache session data
  Future<void> cacheSession(String key, dynamic data) async {
    try {
      final cacheService = ref.read(webCacheServiceProvider);
      await cacheService.setSession(key, data);
      
      final currentState = state.value ?? const SessionCacheState();
      final updatedKeys = Set<String>.from(currentState.sessionKeys)..add(key);
      
      state = AsyncValue.data(
        currentState.copyWith(sessionKeys: updatedKeys),
      );
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Get session data
  Future<dynamic> getSession(String key) async {
    try {
      final cacheService = ref.read(webCacheServiceProvider);
      return await cacheService.getSession(key);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
      return null;
    }
  }

  /// Clear session cache
  Future<void> clearSession() async {
    try {
      final cacheService = ref.read(webCacheServiceProvider);
      await cacheService.clearSession();
      
      state = const AsyncValue.data(SessionCacheState());
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Get session cache size
  int getSessionSize() {
    final cacheService = ref.read(webCacheServiceProvider);
    return cacheService.getSessionSize();
  }
}

/// Local storage state notifier
@riverpod
class LocalStorageNotifier extends _$LocalStorageNotifier {
  @override
  AsyncValue<LocalStorageState> build() {
    return const AsyncValue.data(LocalStorageState());
  }

  /// Set local storage data
  Future<void> setLocal(String key, dynamic data, {Duration? expiration}) async {
    try {
      final cacheService = ref.read(webCacheServiceProvider);
      await cacheService.setLocal(key, data, expiration: expiration);
      
      final currentState = state.value ?? const LocalStorageState();
      final updatedKeys = Set<String>.from(currentState.localKeys)..add(key);
      
      state = AsyncValue.data(
        currentState.copyWith(localKeys: updatedKeys),
      );
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Get local storage data
  Future<dynamic> getLocal(String key) async {
    try {
      final cacheService = ref.read(webCacheServiceProvider);
      return await cacheService.getLocal(key);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
      return null;
    }
  }

  /// Remove local storage data
  Future<void> removeLocal(String key) async {
    try {
      final cacheService = ref.read(webCacheServiceProvider);
      await cacheService.removeLocal(key);
      
      final currentState = state.value ?? const LocalStorageState();
      final updatedKeys = Set<String>.from(currentState.localKeys)..remove(key);
      
      state = AsyncValue.data(
        currentState.copyWith(localKeys: updatedKeys),
      );
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Check if local key exists
  bool hasLocalKey(String key) {
    final cacheService = ref.read(webCacheServiceProvider);
    return cacheService.hasKey(key);
  }
}

/// Web cache state model
@freezed
class WebCacheState with _$WebCacheState {
  const factory WebCacheState({
    @Default({}) Set<String> cachedKeys,
    @Default(0) int cacheSize,
    DateTime? lastCleared,
  }) = _WebCacheState;
}

/// Session cache state model
@freezed
class SessionCacheState with _$SessionCacheState {
  const factory SessionCacheState({
    @Default({}) Set<String> sessionKeys,
    @Default(0) int sessionSize,
    DateTime? lastCleared,
  }) = _SessionCacheState;
}

/// Local storage state model
@freezed
class LocalStorageState with _$LocalStorageState {
  const factory LocalStorageState({
    @Default({}) Set<String> localKeys,
    @Default(0) int localSize,
    DateTime? lastCleared,
  }) = _LocalStorageState;
}
