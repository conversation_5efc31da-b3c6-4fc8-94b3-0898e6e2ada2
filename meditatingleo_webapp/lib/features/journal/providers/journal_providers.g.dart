// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'journal_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$journalRepositoryHash() => r'a12f49e522db2d4257e9330bc1a2a62c17ee7370';

/// Journal repository provider
///
/// Copied from [journalRepository].
@ProviderFor(journalRepository)
final journalRepositoryProvider =
    AutoDisposeProvider<JournalRepository>.internal(
  journalRepository,
  name: r'journalRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$journalRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef JournalRepositoryRef = AutoDisposeProviderRef<JournalRepository>;
String _$journalServiceHash() => r'90530deedcf4ffdd40945d78b5bc097ea90ed731';

/// Journal service provider
///
/// Copied from [journalService].
@ProviderFor(journalService)
final journalServiceProvider = AutoDisposeProvider<JournalService>.internal(
  journalService,
  name: r'journalServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$journalServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef JournalServiceRef = AutoDisposeProviderRef<JournalService>;
String _$journalStreamHash() => r'5af46b52d65243e6959b7328532b09b277126bb0';

/// Journal stream provider for real-time updates
///
/// Copied from [journalStream].
@ProviderFor(journalStream)
final journalStreamProvider =
    AutoDisposeStreamProvider<List<JournalEntryModel>>.internal(
  journalStream,
  name: r'journalStreamProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$journalStreamHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef JournalStreamRef
    = AutoDisposeStreamProviderRef<List<JournalEntryModel>>;
String _$journalNotifierHash() => r'0b37a7f72f87b168610110051b158664e3426064';

/// Journal state notifier for managing journal entries
///
/// Copied from [JournalNotifier].
@ProviderFor(JournalNotifier)
final journalNotifierProvider = AutoDisposeNotifierProvider<JournalNotifier,
    AsyncValue<JournalState>>.internal(
  JournalNotifier.new,
  name: r'journalNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$journalNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$JournalNotifier = AutoDisposeNotifier<AsyncValue<JournalState>>;
String _$journalFormNotifierHash() =>
    r'8a7245ad8687c8df85d6dbaa7b082fb3f04a2db8';

/// Journal form state notifier
///
/// Copied from [JournalFormNotifier].
@ProviderFor(JournalFormNotifier)
final journalFormNotifierProvider =
    AutoDisposeNotifierProvider<JournalFormNotifier, JournalFormState>.internal(
  JournalFormNotifier.new,
  name: r'journalFormNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$journalFormNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$JournalFormNotifier = AutoDisposeNotifier<JournalFormState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
