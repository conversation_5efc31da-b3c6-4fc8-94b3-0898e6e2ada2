import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../widgets/login_form.dart';
import '../../../../core/constants/ui_constants.dart';

/// [LoginPage] provides the main login interface for the web application.
///
/// This page displays the login form in a responsive layout optimized
/// for desktop, tablet, and mobile browsers.
class LoginPage extends ConsumerWidget {
  const LoginPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final mediaQuery = MediaQuery.of(context);
    final isDesktop = mediaQuery.size.width >= UIConstants.desktopBreakpoint;
    final isTablet = mediaQuery.size.width >= UIConstants.tabletBreakpoint && 
                     mediaQuery.size.width < UIConstants.desktopBreakpoint;

    return Scaffold(
      body: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            padding: EdgeInsets.all(
              isDesktop ? UIConstants.spacing32 : UIConstants.spacing16,
            ),
            child: ConstrainedBox(
              constraints: BoxConstraints(
                maxWidth: isDesktop ? 400 : double.infinity,
                minHeight: mediaQuery.size.height - 
                          (isDesktop ? UIConstants.spacing64 : UIConstants.spacing32),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // App logo and title
                  _buildHeader(theme, isDesktop),
                  
                  SizedBox(height: isDesktop ? UIConstants.spacing48 : UIConstants.spacing32),
                  
                  // Login form
                  LoginForm(
                    onSuccess: () => _handleLoginSuccess(context),
                    onRegister: () => _handleRegister(context),
                    onForgotPassword: () => _handleForgotPassword(context),
                  ),
                  
                  SizedBox(height: UIConstants.spacing24),
                  
                  // Footer
                  _buildFooter(theme),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(ThemeData theme, bool isDesktop) {
    return Column(
      children: [
        // App icon/logo
        Container(
          width: isDesktop ? 80 : 64,
          height: isDesktop ? 80 : 64,
          decoration: BoxDecoration(
            color: theme.colorScheme.primary,
            borderRadius: BorderRadius.circular(UIConstants.borderRadius16),
          ),
          child: Icon(
            Icons.self_improvement,
            size: isDesktop ? 48 : 36,
            color: theme.colorScheme.onPrimary,
          ),
        ),
        
        SizedBox(height: UIConstants.spacing16),
        
        // App title
        Text(
          'ClarityByMeditatingLeo',
          style: theme.textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.primary,
          ),
          textAlign: TextAlign.center,
        ),
        
        SizedBox(height: UIConstants.spacing8),
        
        // Subtitle
        Text(
          'Your journey to clarity begins here',
          style: theme.textTheme.bodyLarge?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildFooter(ThemeData theme) {
    return Column(
      children: [
        // Divider
        Divider(
          color: theme.colorScheme.outline.withValues(alpha: 0.3),
        ),
        
        SizedBox(height: UIConstants.spacing16),
        
        // Footer text
        Text(
          '© 2024 ClarityByMeditatingLeo. All rights reserved.',
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
          ),
          textAlign: TextAlign.center,
        ),
        
        SizedBox(height: UIConstants.spacing8),
        
        // Links
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            TextButton(
              onPressed: () => _handlePrivacyPolicy(),
              child: const Text('Privacy Policy'),
            ),
            Text(
              ' • ',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
            TextButton(
              onPressed: () => _handleTermsOfService(),
              child: const Text('Terms of Service'),
            ),
          ],
        ),
      ],
    );
  }

  void _handleLoginSuccess(BuildContext context) {
    // Navigate to home page or dashboard
    Navigator.of(context).pushReplacementNamed('/home');
  }

  void _handleRegister(BuildContext context) {
    // Navigate to registration page
    Navigator.of(context).pushNamed('/register');
  }

  void _handleForgotPassword(BuildContext context) {
    // Navigate to forgot password page
    Navigator.of(context).pushNamed('/forgot-password');
  }

  void _handlePrivacyPolicy() {
    // Open privacy policy
    // This could open a web page or show a dialog
  }

  void _handleTermsOfService() {
    // Open terms of service
    // This could open a web page or show a dialog
  }
}
