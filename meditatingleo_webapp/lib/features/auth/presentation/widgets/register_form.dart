import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/web_auth_providers.dart';
import '../../../../core/constants/ui_constants.dart';
import '../../../../core/utils/validators.dart';

/// [RegisterForm] provides a form for user registration.
///
/// This widget handles user registration with email, password, name,
/// and password confirmation validation.
class RegisterForm extends ConsumerStatefulWidget {
  /// Callback function called on successful registration
  final VoidCallback? onSuccess;

  /// Callback function called when user wants to login
  final VoidCallback? onLogin;

  const RegisterForm({
    super.key,
    this.onSuccess,
    this.onLogin,
  });

  @override
  ConsumerState<RegisterForm> createState() => _RegisterFormState();
}

class _RegisterFormState extends ConsumerState<RegisterForm> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;
  bool _isLoading = false;

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final authState = ref.watch(webAuthNotifierProvider);

    // Listen to auth state changes
    ref.listen<AsyncValue<WebAuthState>>(webAuthNotifierProvider, (previous, next) {
      next.when(
        data: (state) {
          if (state.user != null && !state.isLoading) {
            widget.onSuccess?.call();
          } else if (state.error != null) {
            _showErrorSnackBar(state.error!);
          }
          setState(() {
            _isLoading = state.isLoading;
          });
        },
        loading: () {
          setState(() {
            _isLoading = true;
          });
        },
        error: (error, _) {
          setState(() {
            _isLoading = false;
          });
          _showErrorSnackBar(error.toString());
        },
      );
    });

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(UIConstants.spacing24),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Title
              Text(
                'Create Account',
                style: theme.textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: UIConstants.spacing24),

              // Name field
              TextFormField(
                controller: _nameController,
                textInputAction: TextInputAction.next,
                enabled: !_isLoading,
                decoration: const InputDecoration(
                  labelText: 'Name',
                  hintText: 'Enter your full name',
                  prefixIcon: Icon(Icons.person_outlined),
                ),
                validator: Validators.name,
              ),

              const SizedBox(height: UIConstants.spacing16),

              // Email field
              TextFormField(
                controller: _emailController,
                keyboardType: TextInputType.emailAddress,
                textInputAction: TextInputAction.next,
                enabled: !_isLoading,
                decoration: const InputDecoration(
                  labelText: 'Email',
                  hintText: 'Enter your email address',
                  prefixIcon: Icon(Icons.email_outlined),
                ),
                validator: Validators.email,
              ),

              const SizedBox(height: UIConstants.spacing16),

              // Password field
              TextFormField(
                controller: _passwordController,
                obscureText: _obscurePassword,
                textInputAction: TextInputAction.next,
                enabled: !_isLoading,
                decoration: InputDecoration(
                  labelText: 'Password',
                  hintText: 'Enter your password',
                  prefixIcon: const Icon(Icons.lock_outlined),
                  suffixIcon: IconButton(
                    icon: Icon(
                      _obscurePassword ? Icons.visibility_off : Icons.visibility,
                    ),
                    onPressed: () {
                      setState(() {
                        _obscurePassword = !_obscurePassword;
                      });
                    },
                  ),
                ),
                validator: Validators.password,
              ),

              const SizedBox(height: UIConstants.spacing16),

              // Confirm password field
              TextFormField(
                controller: _confirmPasswordController,
                obscureText: _obscureConfirmPassword,
                textInputAction: TextInputAction.done,
                enabled: !_isLoading,
                decoration: InputDecoration(
                  labelText: 'Confirm Password',
                  hintText: 'Confirm your password',
                  prefixIcon: const Icon(Icons.lock_outlined),
                  suffixIcon: IconButton(
                    icon: Icon(
                      _obscureConfirmPassword ? Icons.visibility_off : Icons.visibility,
                    ),
                    onPressed: () {
                      setState(() {
                        _obscureConfirmPassword = !_obscureConfirmPassword;
                      });
                    },
                  ),
                ),
                validator: (value) => Validators.confirmPassword(
                  value,
                  _passwordController.text,
                ),
                onFieldSubmitted: (_) => _handleSubmit(),
              ),

              const SizedBox(height: UIConstants.spacing24),

              // Create account button
              FilledButton(
                onPressed: _isLoading ? null : _handleSubmit,
                child: _isLoading
                    ? const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                            ),
                          ),
                          SizedBox(width: UIConstants.spacing8),
                          Text('Creating account...'),
                        ],
                      )
                    : const Text('Create Account'),
              ),

              const SizedBox(height: UIConstants.spacing16),

              // Login link
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text('Already have an account? '),
                  TextButton(
                    onPressed: _isLoading ? null : widget.onLogin,
                    child: const Text('Sign in'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _handleSubmit() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final name = _nameController.text.trim();
    final email = _emailController.text.trim();
    final password = _passwordController.text;

    await ref.read(webAuthNotifierProvider.notifier).signUp(
      email,
      password,
      name.isNotEmpty ? name : null,
    );
  }

  void _showErrorSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Theme.of(context).colorScheme.error,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }
}
