import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../providers/web_auth_providers.dart';

/// [OAuthButtons] provides OAuth authentication buttons.
///
/// This widget displays buttons for various OAuth providers like Google,
/// GitHub, Apple, etc. It handles the OAuth flow and provides feedback
/// to the user during the authentication process.
class OAuthButtons extends ConsumerStatefulWidget {
  /// Callback function called when OAuth authentication is successful.
  final VoidCallback? onSuccess;

  /// Whether to show all available providers or just the most common ones.
  final bool showAllProviders;

  /// Custom redirect URL for OAuth flow.
  final String? redirectTo;

  const OAuthButtons({
    super.key,
    this.onSuccess,
    this.showAllProviders = false,
    this.redirectTo,
  });

  @override
  ConsumerState<OAuthButtons> createState() => _OAuthButtonsState();
}

class _OAuthButtonsState extends ConsumerState<OAuthButtons> {
  OAuthProvider? _loadingProvider;

  Future<void> _handleOAuthSignIn(OAuthProvider provider) async {
    setState(() {
      _loadingProvider = provider;
    });

    try {
      final notifier = ref.read(webAuthNotifierProvider.notifier);
      await notifier.signInWithOAuth(
        provider: provider,
        redirectTo: widget.redirectTo,
      );
      
      // OAuth flow initiated successfully
      widget.onSuccess?.call();
    } catch (e) {
      if (mounted) {
        setState(() {
          _loadingProvider = null;
        });
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('OAuth sign in failed: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final notifier = ref.read(webAuthNotifierProvider.notifier);
    final availableProviders = notifier.getAvailableOAuthProviders();
    
    final providersToShow = widget.showAllProviders 
        ? availableProviders 
        : _getCommonProviders(availableProviders);

    return Column(
      children: [
        // Divider with "OR" text
        Row(
          children: [
            const Expanded(child: Divider()),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Text(
                'OR',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
            ),
            const Expanded(child: Divider()),
          ],
        ),
        const SizedBox(height: 16),

        // OAuth provider buttons
        ...providersToShow.map((provider) => Padding(
          padding: const EdgeInsets.only(bottom: 12.0),
          child: _buildOAuthButton(theme, provider),
        )),

        // Show more providers button
        if (!widget.showAllProviders && availableProviders.length > 3) ...[
          const SizedBox(height: 8),
          TextButton.icon(
            onPressed: () {
              // This could open a dialog or navigate to a page with all providers
              _showAllProvidersDialog(context, theme, availableProviders);
            },
            icon: const Icon(Icons.more_horiz),
            label: const Text('More sign-in options'),
          ),
        ],
      ],
    );
  }

  List<OAuthProvider> _getCommonProviders(List<OAuthProvider> allProviders) {
    final commonProviders = [
      OAuthProvider.google,
      OAuthProvider.github,
      OAuthProvider.apple,
    ];
    
    return allProviders.where((provider) => 
      commonProviders.contains(provider)
    ).take(3).toList();
  }

  Widget _buildOAuthButton(ThemeData theme, OAuthProvider provider) {
    final isLoading = _loadingProvider == provider;
    final providerInfo = _getProviderInfo(provider);

    return SizedBox(
      width: double.infinity,
      height: 48,
      child: OutlinedButton.icon(
        onPressed: isLoading ? null : () => _handleOAuthSignIn(provider),
        icon: isLoading 
            ? SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    theme.colorScheme.primary,
                  ),
                ),
              )
            : Icon(
                providerInfo.icon,
                color: providerInfo.color,
                size: 20,
              ),
        label: Text(
          isLoading 
              ? 'Signing in...' 
              : 'Continue with ${providerInfo.name}',
          style: theme.textTheme.labelLarge?.copyWith(
            color: theme.colorScheme.onSurface,
          ),
        ),
        style: OutlinedButton.styleFrom(
          side: BorderSide(
            color: theme.colorScheme.outline.withValues(alpha: 0.5),
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
    );
  }

  _ProviderInfo _getProviderInfo(OAuthProvider provider) {
    switch (provider) {
      case OAuthProvider.google:
        return _ProviderInfo(
          name: 'Google',
          icon: Icons.g_mobiledata,
          color: const Color(0xFF4285F4),
        );
      case OAuthProvider.github:
        return _ProviderInfo(
          name: 'GitHub',
          icon: Icons.code,
          color: const Color(0xFF333333),
        );
      case OAuthProvider.apple:
        return _ProviderInfo(
          name: 'Apple',
          icon: Icons.apple,
          color: const Color(0xFF000000),
        );
      case OAuthProvider.discord:
        return _ProviderInfo(
          name: 'Discord',
          icon: Icons.discord,
          color: const Color(0xFF5865F2),
        );
      case OAuthProvider.facebook:
        return _ProviderInfo(
          name: 'Facebook',
          icon: Icons.facebook,
          color: const Color(0xFF1877F2),
        );
      case OAuthProvider.twitter:
        return _ProviderInfo(
          name: 'Twitter',
          icon: Icons.alternate_email,
          color: const Color(0xFF1DA1F2),
        );
      default:
        return _ProviderInfo(
          name: provider.name,
          icon: Icons.login,
          color: Colors.grey,
        );
    }
  }

  void _showAllProvidersDialog(
    BuildContext context, 
    ThemeData theme, 
    List<OAuthProvider> providers,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Choose sign-in method'),
        content: SizedBox(
          width: double.maxFinite,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: providers.map((provider) => Padding(
              padding: const EdgeInsets.only(bottom: 8.0),
              child: _buildOAuthButton(theme, provider),
            )).toList(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }
}

class _ProviderInfo {
  final String name;
  final IconData icon;
  final Color color;

  const _ProviderInfo({
    required this.name,
    required this.icon,
    required this.color,
  });
}
