import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/web_auth_providers.dart';
import '../../../../core/constants/ui_constants.dart';
import '../../../../core/utils/validators.dart';
import 'oauth_buttons.dart';

/// [LoginForm] provides a form for user authentication.
///
/// This widget handles email/password login with validation,
/// remember me functionality, and responsive design for web.
class LoginForm extends ConsumerStatefulWidget {
  /// Callback function called on successful login
  final VoidCallback? onSuccess;

  /// Callback function called when user wants to register
  final VoidCallback? onRegister;

  /// Callback function called when user wants to reset password
  final VoidCallback? onForgotPassword;

  const LoginForm({
    super.key,
    this.onSuccess,
    this.onRegister,
    this.onForgotPassword,
  });

  @override
  ConsumerState<LoginForm> createState() => _LoginFormState();
}

class _LoginFormState extends ConsumerState<LoginForm> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();

  bool _obscurePassword = true;
  bool _rememberMe = false;
  bool _isLoading = false;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final authState = ref.watch(webAuthNotifierProvider);

    // Listen to auth state changes
    ref.listen<AsyncValue<WebAuthState>>(webAuthNotifierProvider,
        (previous, next) {
      next.when(
        data: (state) {
          if (state.isAuthenticated) {
            widget.onSuccess?.call();
          } else if (state.error != null) {
            _showErrorSnackBar(state.error!);
          }
          setState(() {
            _isLoading = state.isLoading;
          });
        },
        loading: () {
          setState(() {
            _isLoading = true;
          });
        },
        error: (error, _) {
          setState(() {
            _isLoading = false;
          });
          _showErrorSnackBar(error.toString());
        },
      );
    });

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(UIConstants.spacing24),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Title
              Text(
                'Sign In',
                style: theme.textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: UIConstants.spacing24),

              // Email field
              TextFormField(
                controller: _emailController,
                keyboardType: TextInputType.emailAddress,
                textInputAction: TextInputAction.next,
                enabled: !_isLoading,
                decoration: const InputDecoration(
                  labelText: 'Email',
                  hintText: 'Enter your email address',
                  prefixIcon: Icon(Icons.email_outlined),
                ),
                validator: Validators.email,
                onFieldSubmitted: (_) => _focusPassword(),
              ),

              const SizedBox(height: UIConstants.spacing16),

              // Password field
              TextFormField(
                controller: _passwordController,
                obscureText: _obscurePassword,
                textInputAction: TextInputAction.done,
                enabled: !_isLoading,
                decoration: InputDecoration(
                  labelText: 'Password',
                  hintText: 'Enter your password',
                  prefixIcon: const Icon(Icons.lock_outlined),
                  suffixIcon: IconButton(
                    icon: Icon(
                      _obscurePassword
                          ? Icons.visibility_off
                          : Icons.visibility,
                    ),
                    onPressed: () {
                      setState(() {
                        _obscurePassword = !_obscurePassword;
                      });
                    },
                  ),
                ),
                validator: Validators.password,
                onFieldSubmitted: (_) => _handleSubmit(),
              ),

              const SizedBox(height: UIConstants.spacing16),

              // Remember me and forgot password row
              Row(
                children: [
                  // Remember me checkbox
                  Checkbox(
                    value: _rememberMe,
                    onChanged: _isLoading
                        ? null
                        : (value) {
                            setState(() {
                              _rememberMe = value ?? false;
                            });
                          },
                  ),
                  const Text('Remember me'),

                  const Spacer(),

                  // Forgot password link
                  TextButton(
                    onPressed: _isLoading ? null : widget.onForgotPassword,
                    child: const Text('Forgot password?'),
                  ),
                ],
              ),

              const SizedBox(height: UIConstants.spacing24),

              // Sign in button
              FilledButton(
                onPressed: _isLoading ? null : _handleSubmit,
                child: _isLoading
                    ? const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                            ),
                          ),
                          SizedBox(width: UIConstants.spacing8),
                          Text('Signing in...'),
                        ],
                      )
                    : const Text('Sign In'),
              ),

              const SizedBox(height: UIConstants.spacing16),

              // OAuth buttons
              OAuthButtons(
                onSuccess: widget.onSuccess,
                showAllProviders: false,
              ),

              const SizedBox(height: UIConstants.spacing16),

              // Register link
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text("Don't have an account? "),
                  TextButton(
                    onPressed: _isLoading ? null : widget.onRegister,
                    child: const Text('Create account'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _focusPassword() {
    FocusScope.of(context).nextFocus();
  }

  Future<void> _handleSubmit() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final email = _emailController.text.trim();
    final password = _passwordController.text;

    await ref.read(webAuthNotifierProvider.notifier).signInWithEmail(
          email,
          password,
          rememberMe: _rememberMe,
        );
  }

  void _showErrorSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Theme.of(context).colorScheme.error,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }
}
