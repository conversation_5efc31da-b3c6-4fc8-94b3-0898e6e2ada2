import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../shared/widgets/responsive_layout.dart';
import '../../../../shared/models/app_error.dart';
import '../../providers/web_auth_providers.dart';

/// [EmailVerificationForm] provides interface for email verification.
///
/// This form allows users to resend verification emails and provides
/// information about the verification process.
class EmailVerificationForm extends ConsumerStatefulWidget {
  /// The email address that needs verification.
  final String email;

  /// Callback function called when verification is completed.
  final VoidCallback? onVerificationComplete;

  /// Callback function called when user wants to change email.
  final VoidCallback? onChangeEmail;

  const EmailVerificationForm({
    super.key,
    required this.email,
    this.onVerificationComplete,
    this.onChangeEmail,
  });

  @override
  ConsumerState<EmailVerificationForm> createState() =>
      _EmailVerificationFormState();
}

class _EmailVerificationFormState extends ConsumerState<EmailVerificationForm> {
  bool _isResending = false;
  String? _successMessage;
  String? _errorMessage;
  DateTime? _lastResendTime;

  bool get _canResend {
    if (_lastResendTime == null) return true;
    return DateTime.now().difference(_lastResendTime!).inSeconds >= 60;
  }

  int get _remainingSeconds {
    if (_lastResendTime == null) return 0;
    final elapsed = DateTime.now().difference(_lastResendTime!).inSeconds;
    return (60 - elapsed).clamp(0, 60);
  }

  Future<void> _handleResendVerification() async {
    if (!_canResend || _isResending) return;

    setState(() {
      _isResending = true;
      _errorMessage = null;
      _successMessage = null;
    });

    try {
      final notifier = ref.read(webAuthNotifierProvider.notifier);
      final result = await notifier.resendEmailVerification(widget.email);

      if (mounted) {
        result.when(
          success: (_) {
            setState(() {
              _successMessage = 'Verification email sent! Check your inbox.';
              _lastResendTime = DateTime.now();
              _isResending = false;
            });
          },
          failure: (error) {
            setState(() {
              _errorMessage = error.userMessage;
              _isResending = false;
            });
          },
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage =
              'Failed to send verification email. Please try again.';
          _isResending = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return ResponsiveLayout(
      mobile: _buildMobileLayout(theme),
      desktop: _buildDesktopLayout(theme),
    );
  }

  Widget _buildMobileLayout(ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: _buildContent(theme),
    );
  }

  Widget _buildDesktopLayout(ThemeData theme) {
    return Center(
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 500),
        child: Card(
          elevation: 8,
          child: Padding(
            padding: const EdgeInsets.all(32.0),
            child: _buildContent(theme),
          ),
        ),
      ),
    );
  }

  Widget _buildContent(ThemeData theme) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Header Icon
        Icon(
          Icons.mark_email_unread_outlined,
          size: 64,
          color: theme.colorScheme.primary,
        ),
        const SizedBox(height: 24),

        // Title
        Text(
          'Verify Your Email',
          style: theme.textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 16),

        // Description
        Text(
          'We\'ve sent a verification link to:',
          style: theme.textTheme.bodyLarge?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),

        // Email Address
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: theme.colorScheme.surfaceContainerHighest,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            widget.email,
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.onSurface,
            ),
            textAlign: TextAlign.center,
          ),
        ),
        const SizedBox(height: 24),

        // Instructions
        _buildInstructions(theme),
        const SizedBox(height: 32),

        // Success Message
        if (_successMessage != null) ...[
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: theme.colorScheme.primaryContainer,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: theme.colorScheme.primary.withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.check_circle_outline,
                  color: theme.colorScheme.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    _successMessage!,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onPrimaryContainer,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
        ],

        // Error Message
        if (_errorMessage != null) ...[
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: theme.colorScheme.errorContainer,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: theme.colorScheme.error.withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.error_outline,
                  color: theme.colorScheme.error,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    _errorMessage!,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onErrorContainer,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
        ],

        // Resend Button
        FilledButton(
          onPressed:
              _canResend && !_isResending ? _handleResendVerification : null,
          child: _isResending
              ? const SizedBox(
                  height: 20,
                  width: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : Text(_canResend
                  ? 'Resend Verification Email'
                  : 'Resend in ${_remainingSeconds}s'),
        ),
        const SizedBox(height: 16),

        // Change Email Button
        OutlinedButton(
          onPressed: widget.onChangeEmail,
          child: const Text('Use Different Email'),
        ),
        const SizedBox(height: 16),

        // Continue Button (if verified)
        TextButton(
          onPressed: widget.onVerificationComplete,
          child: const Text('I\'ve verified my email'),
        ),
      ],
    );
  }

  Widget _buildInstructions(ThemeData theme) {
    final instructions = [
      'Check your email inbox (and spam folder)',
      'Click the verification link in the email',
      'Return here to continue',
    ];

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerLow,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Next steps:',
            style: theme.textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 12),
          ...instructions.asMap().entries.map((entry) {
            final index = entry.key;
            final instruction = entry.value;
            return Padding(
              padding: const EdgeInsets.only(bottom: 8.0),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary,
                      shape: BoxShape.circle,
                    ),
                    child: Center(
                      child: Text(
                        '${index + 1}',
                        style: theme.textTheme.labelSmall?.copyWith(
                          color: theme.colorScheme.onPrimary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      instruction,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }
}
