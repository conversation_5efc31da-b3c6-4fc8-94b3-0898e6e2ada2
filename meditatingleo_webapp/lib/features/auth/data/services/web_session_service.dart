import 'dart:convert';
import '../models/auth_session_model.dart';
import '../../../../core/security/web_security_utils.dart';
import '../../../../shared/models/result.dart';
import '../../../../shared/models/app_error.dart';
import 'web_storage_interface.dart';

/// [WebSessionService] manages browser session storage and persistence.
///
/// This service handles secure storage of authentication sessions in the browser,
/// including remember me functionality and automatic session restoration.
class WebSessionService {
  static const String _sessionKey = 'meditatingleo_session';
  static const String _rememberMeKey = 'meditatingleo_remember_me';
  static const String _csrfTokenKey = 'meditatingleo_csrf_token';

  final WebStorageInterface _storage;

  /// Creates a WebSessionService with the given storage implementation.
  WebSessionService(this._storage);

  /// Creates a WebSessionService with the default web storage implementation.
  factory WebSessionService.web() {
    // Import here to avoid dart:html issues in tests
    // ignore: implementation_imports
    final webStorage = () {
      try {
        // This will only work in web environment
        return _createWebStorage();
      } catch (e) {
        throw UnsupportedError(
            'WebStorage is only available in web environment');
      }
    }();
    return WebSessionService(webStorage);
  }

  /// Creates the web storage implementation.
  /// This is separated to avoid dart:html import issues.
  static WebStorageInterface _createWebStorage() {
    // This will be replaced with conditional import in production
    throw UnsupportedError('Web storage not available in this environment');
  }

  /// Stores a session in browser storage.
  Future<Result<void, AppError>> storeSession(
    AuthSessionModel session, {
    bool rememberMe = false,
  }) async {
    try {
      final sessionData = session.toJson();
      final encodedData = WebSecurityUtils.encodeForStorage(
        json.encode(sessionData),
      );

      if (rememberMe) {
        // Store in localStorage for persistence
        _storage.setLocalStorage(_sessionKey, encodedData);
        _storage.setLocalStorage(_rememberMeKey, 'true');
      } else {
        // Store in sessionStorage for current session only
        _storage.setSessionStorage(_sessionKey, encodedData);
        _storage.removeLocalStorage(_rememberMeKey);
      }

      return Result.success(null);
    } catch (e) {
      return Result.failure(
        AppError.storage('Failed to store session: ${e.toString()}'),
      );
    }
  }

  /// Retrieves a session from browser storage.
  Future<Result<AuthSessionModel?, AppError>> getStoredSession() async {
    try {
      String? encodedData;

      // Check localStorage first (remember me)
      encodedData = _storage.getLocalStorage(_sessionKey);

      // If not found, check sessionStorage
      encodedData ??= _storage.getSessionStorage(_sessionKey);

      if (encodedData == null) {
        return Result.success(null);
      }

      final decodedData = WebSecurityUtils.decodeFromStorage(encodedData);
      if (decodedData.isEmpty) {
        return Result.success(null);
      }

      final sessionData = json.decode(decodedData) as Map<String, dynamic>;
      final session = AuthSessionModel.fromJson(sessionData);

      // Check if session is expired
      if (session.isExpired) {
        await clearSession();
        return Result.success(null);
      }

      return Result.success(session);
    } catch (e) {
      // Clear corrupted session data
      await clearSession();
      return Result.failure(
        AppError.storage('Failed to retrieve session: ${e.toString()}'),
      );
    }
  }

  /// Clears the stored session.
  Future<Result<void, AppError>> clearSession() async {
    try {
      _storage.removeLocalStorage(_sessionKey);
      _storage.removeSessionStorage(_sessionKey);
      _storage.removeLocalStorage(_rememberMeKey);
      _storage.removeLocalStorage(_csrfTokenKey);

      return Result.success(null);
    } catch (e) {
      return Result.failure(
        AppError.storage('Failed to clear session: ${e.toString()}'),
      );
    }
  }

  /// Checks if remember me is enabled.
  bool isRememberMeEnabled() {
    return _storage.getLocalStorage(_rememberMeKey) == 'true';
  }

  /// Updates the stored session with new data.
  Future<Result<void, AppError>> updateSession(
    AuthSessionModel session,
  ) async {
    try {
      final rememberMe = isRememberMeEnabled();
      return await storeSession(session, rememberMe: rememberMe);
    } catch (e) {
      return Result.failure(
        AppError.storage('Failed to update session: ${e.toString()}'),
      );
    }
  }

  /// Stores CSRF token in browser storage.
  Future<Result<void, AppError>> storeCSRFToken(String token) async {
    try {
      _storage.setSessionStorage(_csrfTokenKey, token);
      WebSecurityUtils.setCSRFToken(token);
      return Result.success(null);
    } catch (e) {
      return Result.failure(
        AppError.storage('Failed to store CSRF token: ${e.toString()}'),
      );
    }
  }

  /// Retrieves CSRF token from browser storage.
  Future<Result<String?, AppError>> getCSRFToken() async {
    try {
      final token = _storage.getSessionStorage(_csrfTokenKey);
      if (token != null) {
        WebSecurityUtils.setCSRFToken(token);
      }
      return Result.success(token);
    } catch (e) {
      return Result.failure(
        AppError.storage('Failed to retrieve CSRF token: ${e.toString()}'),
      );
    }
  }

  /// Generates and stores a new CSRF token.
  Future<Result<String, AppError>> generateCSRFToken() async {
    try {
      final token = WebSecurityUtils.generateCSRFToken();
      final result = await storeCSRFToken(token);

      if (result.isFailure) {
        return Result.failure(result.error!);
      }

      return Result.success(token);
    } catch (e) {
      return Result.failure(
        AppError.storage('Failed to generate CSRF token: ${e.toString()}'),
      );
    }
  }

  /// Validates a CSRF token.
  bool validateCSRFToken(String token) {
    return WebSecurityUtils.validateCSRFToken(token);
  }

  /// Checks if the browser supports the required storage APIs.
  bool isStorageSupported() {
    return _storage.isStorageSupported;
  }

  /// Gets session storage information for debugging.
  Map<String, dynamic> getStorageInfo() {
    return {
      'hasSession': _storage.localStorageContainsKey(_sessionKey) ||
          _storage.sessionStorageContainsKey(_sessionKey),
      'rememberMe': isRememberMeEnabled(),
      'hasCSRFToken': _storage.sessionStorageContainsKey(_csrfTokenKey),
      'storageSupported': isStorageSupported(),
    };
  }

  /// Migrates session from sessionStorage to localStorage (for remember me).
  Future<Result<void, AppError>> enableRememberMe() async {
    try {
      final sessionData = _storage.getSessionStorage(_sessionKey);
      if (sessionData != null) {
        _storage.setLocalStorage(_sessionKey, sessionData);
        _storage.setLocalStorage(_rememberMeKey, 'true');
        _storage.removeSessionStorage(_sessionKey);
      }
      return Result.success(null);
    } catch (e) {
      return Result.failure(
        AppError.storage('Failed to enable remember me: ${e.toString()}'),
      );
    }
  }

  /// Migrates session from localStorage to sessionStorage (disable remember me).
  Future<Result<void, AppError>> disableRememberMe() async {
    try {
      final sessionData = _storage.getLocalStorage(_sessionKey);
      if (sessionData != null) {
        _storage.setSessionStorage(_sessionKey, sessionData);
        _storage.removeLocalStorage(_sessionKey);
        _storage.removeLocalStorage(_rememberMeKey);
      }
      return Result.success(null);
    } catch (e) {
      return Result.failure(
        AppError.storage('Failed to disable remember me: ${e.toString()}'),
      );
    }
  }

  /// Clears all application data from browser storage.
  Future<Result<void, AppError>> clearAllData() async {
    try {
      // Remove all keys that start with our app prefix
      final keysToRemove = <String>[];

      for (int i = 0; i < _storage.localStorageLength; i++) {
        final key = _storage.localStorageKeys.elementAt(i);
        if (key.startsWith('meditatingleo_')) {
          keysToRemove.add(key);
        }
      }

      for (final key in keysToRemove) {
        _storage.removeLocalStorage(key);
      }

      // Clear session storage
      _storage.clearSessionStorage();

      return Result.success(null);
    } catch (e) {
      return Result.failure(
        AppError.storage('Failed to clear all data: ${e.toString()}'),
      );
    }
  }
}
