import 'web_storage_interface.dart';

/// [WebStorageMock] provides a mock implementation for testing.
///
/// This implementation simulates browser storage behavior without
/// requiring dart:html, making it suitable for unit tests.
class WebStorageMock implements WebStorageInterface {
  final Map<String, String> _localStorage = {};
  final Map<String, String> _sessionStorage = {};

  @override
  void setLocalStorage(String key, String value) {
    _localStorage[key] = value;
  }

  @override
  String? getLocalStorage(String key) {
    return _localStorage[key];
  }

  @override
  void removeLocalStorage(String key) {
    _localStorage.remove(key);
  }

  @override
  void setSessionStorage(String key, String value) {
    _sessionStorage[key] = value;
  }

  @override
  String? getSessionStorage(String key) {
    return _sessionStorage[key];
  }

  @override
  void removeSessionStorage(String key) {
    _sessionStorage.remove(key);
  }

  @override
  bool localStorageContainsKey(String key) {
    return _localStorage.containsKey(key);
  }

  @override
  bool sessionStorageContainsKey(String key) {
    return _sessionStorage.containsKey(key);
  }

  @override
  int get localStorageLength => _localStorage.length;

  @override
  Iterable<String> get localStorageKeys => _localStorage.keys;

  @override
  void clearSessionStorage() {
    _sessionStorage.clear();
  }

  @override
  bool get isStorageSupported => true;

  /// Clears all storage for testing
  void clearAll() {
    _localStorage.clear();
    _sessionStorage.clear();
  }

  /// Gets localStorage contents for testing
  Map<String, String> get localStorageContents => Map.from(_localStorage);

  /// Gets sessionStorage contents for testing
  Map<String, String> get sessionStorageContents => Map.from(_sessionStorage);
}
