/// [WebStorageInterface] defines the contract for web storage operations.
///
/// This interface allows for different implementations for web and testing
/// environments, avoiding dart:html dependency issues in tests.
abstract class WebStorageInterface {
  /// Stores a value in localStorage
  void setLocalStorage(String key, String value);
  
  /// Gets a value from localStorage
  String? getLocalStorage(String key);
  
  /// Removes a value from localStorage
  void removeLocalStorage(String key);
  
  /// Stores a value in sessionStorage
  void setSessionStorage(String key, String value);
  
  /// Gets a value from sessionStorage
  String? getSessionStorage(String key);
  
  /// Removes a value from sessionStorage
  void removeSessionStorage(String key);
  
  /// Checks if localStorage contains a key
  bool localStorageContainsKey(String key);
  
  /// Checks if sessionStorage contains a key
  bool sessionStorageContainsKey(String key);
  
  /// Gets the length of localStorage
  int get localStorageLength;
  
  /// Gets all keys from localStorage
  Iterable<String> get localStorageKeys;
  
  /// Clears sessionStorage
  void clearSessionStorage();
  
  /// Checks if storage is supported
  bool get isStorageSupported;
}
