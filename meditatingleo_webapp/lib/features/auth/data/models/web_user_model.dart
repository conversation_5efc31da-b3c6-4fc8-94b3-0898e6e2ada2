import 'package:freezed_annotation/freezed_annotation.dart';

part 'web_user_model.freezed.dart';
part 'web_user_model.g.dart';

/// [WebUserModel] represents a user in the web application.
///
/// This model contains user information specific to web authentication
/// and session management, including email verification status and
/// browser-specific user data.
@freezed
class WebUserModel with _$WebUserModel {
  /// Creates a [WebUserModel] instance.
  const factory WebUserModel({
    /// Unique user identifier
    required String id,
    
    /// User's email address
    required String email,
    
    /// User's display name
    String? name,
    
    /// URL to user's avatar image
    @<PERSON>sonKey(name: 'avatar_url')
    String? avatarUrl,
    
    /// Timestamp when the user account was created
    @<PERSON>sonKey(name: 'created_at')
    required DateTime createdAt,
    
    /// Timestamp of the user's last sign-in
    @JsonKey(name: 'last_sign_in_at')
    DateTime? lastSignInAt,
    
    /// Timestamp when the user's email was confirmed
    @<PERSON><PERSON><PERSON><PERSON>(name: 'email_confirmed_at')
    DateTime? emailConfirmedAt,
  }) = _WebUserModel;

  /// Creates a [WebUserModel] from JSON.
  factory WebUserModel.fromJson(Map<String, dynamic> json) =>
      _$WebUserModelFromJson(json);
}

/// Extension methods for [WebUserModel] validation and utility functions.
extension WebUserModelExtensions on WebUserModel {
  /// Validates if the email format is correct.
  bool get isEmailValid {
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    return emailRegex.hasMatch(email);
  }

  /// Checks if the user's email has been confirmed.
  bool get isEmailConfirmed => emailConfirmedAt != null;

  /// Gets the user's display name or email if name is not available.
  String get displayName => name ?? email;

  /// Checks if the user has a profile picture.
  bool get hasAvatar => avatarUrl != null && avatarUrl!.isNotEmpty;

  /// Gets the initials from the user's name or email.
  String get initials {
    if (name != null && name!.isNotEmpty) {
      final parts = name!.split(' ');
      if (parts.length >= 2) {
        return '${parts[0][0]}${parts[1][0]}'.toUpperCase();
      }
      return name![0].toUpperCase();
    }
    return email[0].toUpperCase();
  }

  /// Checks if the user account is active and verified.
  bool get isActive => isEmailConfirmed && isEmailValid;

  /// Gets the time since the user was created.
  Duration get accountAge => DateTime.now().difference(createdAt);

  /// Gets the time since the user's last sign-in.
  Duration? get timeSinceLastSignIn {
    if (lastSignInAt == null) return null;
    return DateTime.now().difference(lastSignInAt!);
  }

  /// Checks if the user is a new user (created within the last 24 hours).
  bool get isNewUser => accountAge.inHours < 24;

  /// Creates a copy of this user with updated profile information.
  WebUserModel updateProfile({
    String? name,
    String? avatarUrl,
  }) {
    return copyWith(
      name: name ?? this.name,
      avatarUrl: avatarUrl ?? this.avatarUrl,
    );
  }

  /// Creates a copy of this user with updated sign-in timestamp.
  WebUserModel updateLastSignIn() {
    return copyWith(lastSignInAt: DateTime.now());
  }

  /// Creates a copy of this user with confirmed email.
  WebUserModel confirmEmail() {
    return copyWith(emailConfirmedAt: DateTime.now());
  }
}
