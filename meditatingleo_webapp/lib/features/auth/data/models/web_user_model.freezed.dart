// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'web_user_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

WebUserModel _$WebUserModelFromJson(Map<String, dynamic> json) {
  return _WebUserModel.fromJson(json);
}

/// @nodoc
mixin _$WebUserModel {
  /// Unique user identifier
  String get id => throw _privateConstructorUsedError;

  /// User's email address
  String get email => throw _privateConstructorUsedError;

  /// User's display name
  String? get name => throw _privateConstructorUsedError;

  /// URL to user's avatar image
  @JsonKey(name: 'avatar_url')
  String? get avatarUrl => throw _privateConstructorUsedError;

  /// Timestamp when the user account was created
  @JsonKey(name: 'created_at')
  DateTime get createdAt => throw _privateConstructorUsedError;

  /// Timestamp of the user's last sign-in
  @JsonKey(name: 'last_sign_in_at')
  DateTime? get lastSignInAt => throw _privateConstructorUsedError;

  /// Timestamp when the user's email was confirmed
  @JsonKey(name: 'email_confirmed_at')
  DateTime? get emailConfirmedAt => throw _privateConstructorUsedError;

  /// Serializes this WebUserModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of WebUserModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $WebUserModelCopyWith<WebUserModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WebUserModelCopyWith<$Res> {
  factory $WebUserModelCopyWith(
          WebUserModel value, $Res Function(WebUserModel) then) =
      _$WebUserModelCopyWithImpl<$Res, WebUserModel>;
  @useResult
  $Res call(
      {String id,
      String email,
      String? name,
      @JsonKey(name: 'avatar_url') String? avatarUrl,
      @JsonKey(name: 'created_at') DateTime createdAt,
      @JsonKey(name: 'last_sign_in_at') DateTime? lastSignInAt,
      @JsonKey(name: 'email_confirmed_at') DateTime? emailConfirmedAt});
}

/// @nodoc
class _$WebUserModelCopyWithImpl<$Res, $Val extends WebUserModel>
    implements $WebUserModelCopyWith<$Res> {
  _$WebUserModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of WebUserModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? email = null,
    Object? name = freezed,
    Object? avatarUrl = freezed,
    Object? createdAt = null,
    Object? lastSignInAt = freezed,
    Object? emailConfirmedAt = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      avatarUrl: freezed == avatarUrl
          ? _value.avatarUrl
          : avatarUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      lastSignInAt: freezed == lastSignInAt
          ? _value.lastSignInAt
          : lastSignInAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      emailConfirmedAt: freezed == emailConfirmedAt
          ? _value.emailConfirmedAt
          : emailConfirmedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$WebUserModelImplCopyWith<$Res>
    implements $WebUserModelCopyWith<$Res> {
  factory _$$WebUserModelImplCopyWith(
          _$WebUserModelImpl value, $Res Function(_$WebUserModelImpl) then) =
      __$$WebUserModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String email,
      String? name,
      @JsonKey(name: 'avatar_url') String? avatarUrl,
      @JsonKey(name: 'created_at') DateTime createdAt,
      @JsonKey(name: 'last_sign_in_at') DateTime? lastSignInAt,
      @JsonKey(name: 'email_confirmed_at') DateTime? emailConfirmedAt});
}

/// @nodoc
class __$$WebUserModelImplCopyWithImpl<$Res>
    extends _$WebUserModelCopyWithImpl<$Res, _$WebUserModelImpl>
    implements _$$WebUserModelImplCopyWith<$Res> {
  __$$WebUserModelImplCopyWithImpl(
      _$WebUserModelImpl _value, $Res Function(_$WebUserModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of WebUserModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? email = null,
    Object? name = freezed,
    Object? avatarUrl = freezed,
    Object? createdAt = null,
    Object? lastSignInAt = freezed,
    Object? emailConfirmedAt = freezed,
  }) {
    return _then(_$WebUserModelImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      avatarUrl: freezed == avatarUrl
          ? _value.avatarUrl
          : avatarUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      lastSignInAt: freezed == lastSignInAt
          ? _value.lastSignInAt
          : lastSignInAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      emailConfirmedAt: freezed == emailConfirmedAt
          ? _value.emailConfirmedAt
          : emailConfirmedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$WebUserModelImpl implements _WebUserModel {
  const _$WebUserModelImpl(
      {required this.id,
      required this.email,
      this.name,
      @JsonKey(name: 'avatar_url') this.avatarUrl,
      @JsonKey(name: 'created_at') required this.createdAt,
      @JsonKey(name: 'last_sign_in_at') this.lastSignInAt,
      @JsonKey(name: 'email_confirmed_at') this.emailConfirmedAt});

  factory _$WebUserModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$WebUserModelImplFromJson(json);

  /// Unique user identifier
  @override
  final String id;

  /// User's email address
  @override
  final String email;

  /// User's display name
  @override
  final String? name;

  /// URL to user's avatar image
  @override
  @JsonKey(name: 'avatar_url')
  final String? avatarUrl;

  /// Timestamp when the user account was created
  @override
  @JsonKey(name: 'created_at')
  final DateTime createdAt;

  /// Timestamp of the user's last sign-in
  @override
  @JsonKey(name: 'last_sign_in_at')
  final DateTime? lastSignInAt;

  /// Timestamp when the user's email was confirmed
  @override
  @JsonKey(name: 'email_confirmed_at')
  final DateTime? emailConfirmedAt;

  @override
  String toString() {
    return 'WebUserModel(id: $id, email: $email, name: $name, avatarUrl: $avatarUrl, createdAt: $createdAt, lastSignInAt: $lastSignInAt, emailConfirmedAt: $emailConfirmedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WebUserModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.avatarUrl, avatarUrl) ||
                other.avatarUrl == avatarUrl) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.lastSignInAt, lastSignInAt) ||
                other.lastSignInAt == lastSignInAt) &&
            (identical(other.emailConfirmedAt, emailConfirmedAt) ||
                other.emailConfirmedAt == emailConfirmedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, email, name, avatarUrl,
      createdAt, lastSignInAt, emailConfirmedAt);

  /// Create a copy of WebUserModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$WebUserModelImplCopyWith<_$WebUserModelImpl> get copyWith =>
      __$$WebUserModelImplCopyWithImpl<_$WebUserModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WebUserModelImplToJson(
      this,
    );
  }
}

abstract class _WebUserModel implements WebUserModel {
  const factory _WebUserModel(
      {required final String id,
      required final String email,
      final String? name,
      @JsonKey(name: 'avatar_url') final String? avatarUrl,
      @JsonKey(name: 'created_at') required final DateTime createdAt,
      @JsonKey(name: 'last_sign_in_at') final DateTime? lastSignInAt,
      @JsonKey(name: 'email_confirmed_at')
      final DateTime? emailConfirmedAt}) = _$WebUserModelImpl;

  factory _WebUserModel.fromJson(Map<String, dynamic> json) =
      _$WebUserModelImpl.fromJson;

  /// Unique user identifier
  @override
  String get id;

  /// User's email address
  @override
  String get email;

  /// User's display name
  @override
  String? get name;

  /// URL to user's avatar image
  @override
  @JsonKey(name: 'avatar_url')
  String? get avatarUrl;

  /// Timestamp when the user account was created
  @override
  @JsonKey(name: 'created_at')
  DateTime get createdAt;

  /// Timestamp of the user's last sign-in
  @override
  @JsonKey(name: 'last_sign_in_at')
  DateTime? get lastSignInAt;

  /// Timestamp when the user's email was confirmed
  @override
  @JsonKey(name: 'email_confirmed_at')
  DateTime? get emailConfirmedAt;

  /// Create a copy of WebUserModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$WebUserModelImplCopyWith<_$WebUserModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
