import 'package:freezed_annotation/freezed_annotation.dart';

part 'auth_session_model.freezed.dart';
part 'auth_session_model.g.dart';

/// [AuthSessionModel] represents an authentication session in the web application.
///
/// This model contains session information including access tokens,
/// refresh tokens, and expiration details for secure web authentication.
@freezed
class AuthSessionModel with _$AuthSessionModel {
  /// Creates an [AuthSessionModel] instance.
  const factory AuthSessionModel({
    /// JWT access token for API authentication
    @JsonKey(name: 'access_token') required String accessToken,

    /// Refresh token for obtaining new access tokens
    @JsonKey(name: 'refresh_token') String? refreshToken,

    /// Token expiration time in seconds
    @JsonKey(name: 'expires_in') required int expiresIn,

    /// Absolute expiration timestamp
    @JsonKey(
        name: 'expires_at',
        fromJson: AuthSessionModel._fromTimestamp,
        toJson: AuthSessionModel._toTimestamp)
    DateTime? expiresAt,

    /// Token type (usually 'bearer')
    @JsonKey(name: 'token_type') @Default('bearer') String tokenType,

    /// User ID associated with this session
    @JsonKey(name: 'user_id') required String userId,
  }) = _AuthSessionModel;

  /// Creates an [AuthSessionModel] from JSON.
  factory AuthSessionModel.fromJson(Map<String, dynamic> json) =>
      _$AuthSessionModelFromJson(json);

  /// Creates a test session for testing purposes.
  factory AuthSessionModel.createTestSession() {
    return AuthSessionModel(
      accessToken: 'test-access-token',
      expiresIn: 3600,
      expiresAt: DateTime.now().add(const Duration(hours: 1)),
      userId: 'test-user-id',
      refreshToken: 'test-refresh-token',
      tokenType: 'Bearer',
    );
  }

  /// Converts timestamp to DateTime
  static DateTime? _fromTimestamp(dynamic timestamp) {
    if (timestamp == null) return null;
    if (timestamp is int) {
      return DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
    }
    if (timestamp is String) {
      return DateTime.parse(timestamp);
    }
    return null;
  }

  /// Converts DateTime to timestamp
  static int? _toTimestamp(DateTime? dateTime) {
    return dateTime != null ? dateTime.millisecondsSinceEpoch ~/ 1000 : null;
  }
}

/// Extension methods for [AuthSessionModel] validation and utility functions.
extension AuthSessionModelExtensions on AuthSessionModel {
  /// Checks if the session has expired.
  bool get isExpired {
    if (expiresAt == null) return false;
    return DateTime.now().isAfter(expiresAt!);
  }

  /// Checks if the session is expiring soon (within 5 minutes).
  bool get isExpiringSoon {
    if (expiresAt == null) return false;
    final fiveMinutesFromNow = DateTime.now().add(const Duration(minutes: 5));
    return expiresAt!.isBefore(fiveMinutesFromNow);
  }

  /// Checks if the session is valid (has access token and not expired).
  bool get isValid {
    return accessToken.isNotEmpty && !isExpired;
  }

  /// Gets the remaining time until expiration.
  Duration get remainingTime {
    if (expiresAt == null) return Duration.zero;
    final now = DateTime.now();
    if (expiresAt!.isBefore(now)) return Duration.zero;
    return expiresAt!.difference(now);
  }

  /// Gets the remaining time as a percentage (0.0 to 1.0).
  double get remainingTimePercentage {
    if (expiresAt == null) return 1.0;
    final totalDuration = Duration(seconds: expiresIn);
    final remaining = remainingTime;
    if (totalDuration.inSeconds == 0) return 0.0;
    return remaining.inSeconds / totalDuration.inSeconds;
  }

  /// Checks if the session has a refresh token.
  bool get hasRefreshToken => refreshToken != null && refreshToken!.isNotEmpty;

  /// Checks if the session can be refreshed.
  bool get canRefresh => hasRefreshToken && !isExpired;

  /// Gets the authorization header value.
  String get authorizationHeader => '$tokenType $accessToken';

  /// Creates a copy of this session with a new access token.
  AuthSessionModel refreshWithNewToken({
    required String newAccessToken,
    String? newRefreshToken,
    int? newExpiresIn,
  }) {
    final now = DateTime.now();
    final expiresIn = newExpiresIn ?? this.expiresIn;

    return copyWith(
      accessToken: newAccessToken,
      refreshToken: newRefreshToken ?? refreshToken,
      expiresIn: expiresIn,
      expiresAt: now.add(Duration(seconds: expiresIn)),
    );
  }

  /// Creates a copy of this session with updated expiration.
  AuthSessionModel updateExpiration(DateTime newExpiresAt) {
    return copyWith(expiresAt: newExpiresAt);
  }

  /// Checks if the session needs to be refreshed soon.
  bool get needsRefresh {
    return isExpiringSoon && canRefresh;
  }

  /// Gets a human-readable string of the remaining time.
  String get remainingTimeString {
    final remaining = remainingTime;
    if (remaining.inDays > 0) {
      return '${remaining.inDays}d ${remaining.inHours % 24}h';
    } else if (remaining.inHours > 0) {
      return '${remaining.inHours}h ${remaining.inMinutes % 60}m';
    } else if (remaining.inMinutes > 0) {
      return '${remaining.inMinutes}m';
    } else {
      return '${remaining.inSeconds}s';
    }
  }

  /// Creates a session for testing purposes.
  static AuthSessionModel createTestSession({
    String accessToken = 'test-access-token',
    String? refreshToken = 'test-refresh-token',
    int expiresIn = 3600,
    String userId = 'test-user-id',
    DateTime? expiresAt,
  }) {
    return AuthSessionModel(
      accessToken: accessToken,
      refreshToken: refreshToken,
      expiresIn: expiresIn,
      expiresAt: expiresAt ?? DateTime.now().add(Duration(seconds: expiresIn)),
      userId: userId,
    );
  }
}
