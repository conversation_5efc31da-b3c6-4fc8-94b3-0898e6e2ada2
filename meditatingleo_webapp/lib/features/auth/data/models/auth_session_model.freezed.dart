// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'auth_session_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AuthSessionModel _$AuthSessionModelFromJson(Map<String, dynamic> json) {
  return _AuthSessionModel.fromJson(json);
}

/// @nodoc
mixin _$AuthSessionModel {
  /// JWT access token for API authentication
  @JsonKey(name: 'access_token')
  String get accessToken => throw _privateConstructorUsedError;

  /// Refresh token for obtaining new access tokens
  @JsonKey(name: 'refresh_token')
  String? get refreshToken => throw _privateConstructorUsedError;

  /// Token expiration time in seconds
  @JsonKey(name: 'expires_in')
  int get expiresIn => throw _privateConstructorUsedError;

  /// Absolute expiration timestamp
  @JsonKey(
      name: 'expires_at',
      fromJson: AuthSessionModel._fromTimestamp,
      toJson: AuthSessionModel._toTimestamp)
  DateTime? get expiresAt => throw _privateConstructorUsedError;

  /// Token type (usually 'bearer')
  @JsonKey(name: 'token_type')
  String get tokenType => throw _privateConstructorUsedError;

  /// User ID associated with this session
  @JsonKey(name: 'user_id')
  String get userId => throw _privateConstructorUsedError;

  /// Serializes this AuthSessionModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AuthSessionModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AuthSessionModelCopyWith<AuthSessionModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AuthSessionModelCopyWith<$Res> {
  factory $AuthSessionModelCopyWith(
          AuthSessionModel value, $Res Function(AuthSessionModel) then) =
      _$AuthSessionModelCopyWithImpl<$Res, AuthSessionModel>;
  @useResult
  $Res call(
      {@JsonKey(name: 'access_token') String accessToken,
      @JsonKey(name: 'refresh_token') String? refreshToken,
      @JsonKey(name: 'expires_in') int expiresIn,
      @JsonKey(
          name: 'expires_at',
          fromJson: AuthSessionModel._fromTimestamp,
          toJson: AuthSessionModel._toTimestamp)
      DateTime? expiresAt,
      @JsonKey(name: 'token_type') String tokenType,
      @JsonKey(name: 'user_id') String userId});
}

/// @nodoc
class _$AuthSessionModelCopyWithImpl<$Res, $Val extends AuthSessionModel>
    implements $AuthSessionModelCopyWith<$Res> {
  _$AuthSessionModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AuthSessionModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accessToken = null,
    Object? refreshToken = freezed,
    Object? expiresIn = null,
    Object? expiresAt = freezed,
    Object? tokenType = null,
    Object? userId = null,
  }) {
    return _then(_value.copyWith(
      accessToken: null == accessToken
          ? _value.accessToken
          : accessToken // ignore: cast_nullable_to_non_nullable
              as String,
      refreshToken: freezed == refreshToken
          ? _value.refreshToken
          : refreshToken // ignore: cast_nullable_to_non_nullable
              as String?,
      expiresIn: null == expiresIn
          ? _value.expiresIn
          : expiresIn // ignore: cast_nullable_to_non_nullable
              as int,
      expiresAt: freezed == expiresAt
          ? _value.expiresAt
          : expiresAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      tokenType: null == tokenType
          ? _value.tokenType
          : tokenType // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AuthSessionModelImplCopyWith<$Res>
    implements $AuthSessionModelCopyWith<$Res> {
  factory _$$AuthSessionModelImplCopyWith(_$AuthSessionModelImpl value,
          $Res Function(_$AuthSessionModelImpl) then) =
      __$$AuthSessionModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'access_token') String accessToken,
      @JsonKey(name: 'refresh_token') String? refreshToken,
      @JsonKey(name: 'expires_in') int expiresIn,
      @JsonKey(
          name: 'expires_at',
          fromJson: AuthSessionModel._fromTimestamp,
          toJson: AuthSessionModel._toTimestamp)
      DateTime? expiresAt,
      @JsonKey(name: 'token_type') String tokenType,
      @JsonKey(name: 'user_id') String userId});
}

/// @nodoc
class __$$AuthSessionModelImplCopyWithImpl<$Res>
    extends _$AuthSessionModelCopyWithImpl<$Res, _$AuthSessionModelImpl>
    implements _$$AuthSessionModelImplCopyWith<$Res> {
  __$$AuthSessionModelImplCopyWithImpl(_$AuthSessionModelImpl _value,
      $Res Function(_$AuthSessionModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of AuthSessionModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accessToken = null,
    Object? refreshToken = freezed,
    Object? expiresIn = null,
    Object? expiresAt = freezed,
    Object? tokenType = null,
    Object? userId = null,
  }) {
    return _then(_$AuthSessionModelImpl(
      accessToken: null == accessToken
          ? _value.accessToken
          : accessToken // ignore: cast_nullable_to_non_nullable
              as String,
      refreshToken: freezed == refreshToken
          ? _value.refreshToken
          : refreshToken // ignore: cast_nullable_to_non_nullable
              as String?,
      expiresIn: null == expiresIn
          ? _value.expiresIn
          : expiresIn // ignore: cast_nullable_to_non_nullable
              as int,
      expiresAt: freezed == expiresAt
          ? _value.expiresAt
          : expiresAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      tokenType: null == tokenType
          ? _value.tokenType
          : tokenType // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AuthSessionModelImpl implements _AuthSessionModel {
  const _$AuthSessionModelImpl(
      {@JsonKey(name: 'access_token') required this.accessToken,
      @JsonKey(name: 'refresh_token') this.refreshToken,
      @JsonKey(name: 'expires_in') required this.expiresIn,
      @JsonKey(
          name: 'expires_at',
          fromJson: AuthSessionModel._fromTimestamp,
          toJson: AuthSessionModel._toTimestamp)
      this.expiresAt,
      @JsonKey(name: 'token_type') this.tokenType = 'bearer',
      @JsonKey(name: 'user_id') required this.userId});

  factory _$AuthSessionModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$AuthSessionModelImplFromJson(json);

  /// JWT access token for API authentication
  @override
  @JsonKey(name: 'access_token')
  final String accessToken;

  /// Refresh token for obtaining new access tokens
  @override
  @JsonKey(name: 'refresh_token')
  final String? refreshToken;

  /// Token expiration time in seconds
  @override
  @JsonKey(name: 'expires_in')
  final int expiresIn;

  /// Absolute expiration timestamp
  @override
  @JsonKey(
      name: 'expires_at',
      fromJson: AuthSessionModel._fromTimestamp,
      toJson: AuthSessionModel._toTimestamp)
  final DateTime? expiresAt;

  /// Token type (usually 'bearer')
  @override
  @JsonKey(name: 'token_type')
  final String tokenType;

  /// User ID associated with this session
  @override
  @JsonKey(name: 'user_id')
  final String userId;

  @override
  String toString() {
    return 'AuthSessionModel(accessToken: $accessToken, refreshToken: $refreshToken, expiresIn: $expiresIn, expiresAt: $expiresAt, tokenType: $tokenType, userId: $userId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AuthSessionModelImpl &&
            (identical(other.accessToken, accessToken) ||
                other.accessToken == accessToken) &&
            (identical(other.refreshToken, refreshToken) ||
                other.refreshToken == refreshToken) &&
            (identical(other.expiresIn, expiresIn) ||
                other.expiresIn == expiresIn) &&
            (identical(other.expiresAt, expiresAt) ||
                other.expiresAt == expiresAt) &&
            (identical(other.tokenType, tokenType) ||
                other.tokenType == tokenType) &&
            (identical(other.userId, userId) || other.userId == userId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, accessToken, refreshToken,
      expiresIn, expiresAt, tokenType, userId);

  /// Create a copy of AuthSessionModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AuthSessionModelImplCopyWith<_$AuthSessionModelImpl> get copyWith =>
      __$$AuthSessionModelImplCopyWithImpl<_$AuthSessionModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AuthSessionModelImplToJson(
      this,
    );
  }
}

abstract class _AuthSessionModel implements AuthSessionModel {
  const factory _AuthSessionModel(
          {@JsonKey(name: 'access_token') required final String accessToken,
          @JsonKey(name: 'refresh_token') final String? refreshToken,
          @JsonKey(name: 'expires_in') required final int expiresIn,
          @JsonKey(
              name: 'expires_at',
              fromJson: AuthSessionModel._fromTimestamp,
              toJson: AuthSessionModel._toTimestamp)
          final DateTime? expiresAt,
          @JsonKey(name: 'token_type') final String tokenType,
          @JsonKey(name: 'user_id') required final String userId}) =
      _$AuthSessionModelImpl;

  factory _AuthSessionModel.fromJson(Map<String, dynamic> json) =
      _$AuthSessionModelImpl.fromJson;

  /// JWT access token for API authentication
  @override
  @JsonKey(name: 'access_token')
  String get accessToken;

  /// Refresh token for obtaining new access tokens
  @override
  @JsonKey(name: 'refresh_token')
  String? get refreshToken;

  /// Token expiration time in seconds
  @override
  @JsonKey(name: 'expires_in')
  int get expiresIn;

  /// Absolute expiration timestamp
  @override
  @JsonKey(
      name: 'expires_at',
      fromJson: AuthSessionModel._fromTimestamp,
      toJson: AuthSessionModel._toTimestamp)
  DateTime? get expiresAt;

  /// Token type (usually 'bearer')
  @override
  @JsonKey(name: 'token_type')
  String get tokenType;

  /// User ID associated with this session
  @override
  @JsonKey(name: 'user_id')
  String get userId;

  /// Create a copy of AuthSessionModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AuthSessionModelImplCopyWith<_$AuthSessionModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
