// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'auth_session_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AuthSessionModelImpl _$$AuthSessionModelImplFromJson(
        Map<String, dynamic> json) =>
    _$AuthSessionModelImpl(
      accessToken: json['access_token'] as String,
      refreshToken: json['refresh_token'] as String?,
      expiresIn: (json['expires_in'] as num).toInt(),
      expiresAt: AuthSessionModel._fromTimestamp(json['expires_at']),
      tokenType: json['token_type'] as String? ?? 'bearer',
      userId: json['user_id'] as String,
    );

Map<String, dynamic> _$$AuthSessionModelImplToJson(
        _$AuthSessionModelImpl instance) =>
    <String, dynamic>{
      'access_token': instance.accessToken,
      'refresh_token': instance.refreshToken,
      'expires_in': instance.expiresIn,
      'expires_at': AuthSessionModel._toTimestamp(instance.expiresAt),
      'token_type': instance.tokenType,
      'user_id': instance.userId,
    };
