// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'web_user_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$WebUserModelImpl _$$WebUserModelImplFromJson(Map<String, dynamic> json) =>
    _$WebUserModelImpl(
      id: json['id'] as String,
      email: json['email'] as String,
      name: json['name'] as String?,
      avatarUrl: json['avatar_url'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      lastSignInAt: json['last_sign_in_at'] == null
          ? null
          : DateTime.parse(json['last_sign_in_at'] as String),
      emailConfirmedAt: json['email_confirmed_at'] == null
          ? null
          : DateTime.parse(json['email_confirmed_at'] as String),
    );

Map<String, dynamic> _$$WebUserModelImplToJson(_$WebUserModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'email': instance.email,
      'name': instance.name,
      'avatar_url': instance.avatarUrl,
      'created_at': instance.createdAt.toIso8601String(),
      'last_sign_in_at': instance.lastSignInAt?.toIso8601String(),
      'email_confirmed_at': instance.emailConfirmedAt?.toIso8601String(),
    };
