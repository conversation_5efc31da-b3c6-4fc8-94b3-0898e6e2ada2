import 'package:freezed_annotation/freezed_annotation.dart';
import 'web_user_model.dart';
import 'auth_session_model.dart';

part 'auth_response_model.freezed.dart';
part 'auth_response_model.g.dart';

/// [AuthResponseModel] represents the response from authentication operations.
///
/// This model contains both user information and session data returned
/// from successful authentication requests.
@freezed
class AuthResponseModel with _$AuthResponseModel {
  /// Creates an [AuthResponseModel] instance.
  const factory AuthResponseModel({
    /// User information
    required WebUserModel user,
    
    /// Session information
    required AuthSessionModel session,
    
    /// Additional metadata
    Map<String, dynamic>? metadata,
  }) = _AuthResponseModel;

  /// Creates an [AuthResponseModel] from JSON.
  factory AuthResponseModel.fromJson(Map<String, dynamic> json) =>
      _$AuthResponseModelFromJson(json);
}

/// Extension methods for [AuthResponseModel] utility functions.
extension AuthResponseModelExtensions on AuthResponseModel {
  /// Checks if the authentication response is valid.
  bool get isValid {
    return user.isEmailValid && session.isValid;
  }

  /// Checks if the user is fully authenticated (email confirmed and session valid).
  bool get isFullyAuthenticated {
    return user.isEmailConfirmed && session.isValid;
  }

  /// Gets the authorization header for API requests.
  String get authorizationHeader => session.authorizationHeader;

  /// Checks if the session needs to be refreshed.
  bool get needsRefresh => session.needsRefresh;

  /// Creates a copy with updated session.
  AuthResponseModel updateSession(AuthSessionModel newSession) {
    return copyWith(session: newSession);
  }

  /// Creates a copy with updated user.
  AuthResponseModel updateUser(WebUserModel newUser) {
    return copyWith(user: newUser);
  }
}
