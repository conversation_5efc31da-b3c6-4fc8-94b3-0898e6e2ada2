import 'package:supabase_flutter/supabase_flutter.dart';
import '../services/web_auth_service.dart';
import '../services/web_session_service.dart';
import '../models/web_user_model.dart';
import '../models/auth_session_model.dart';
import '../../../../shared/models/result.dart';
import '../../../../shared/models/app_error.dart';

/// [WebAuthRepository] provides a unified interface for authentication operations.
///
/// This repository coordinates between the authentication service and session
/// management service to provide a complete authentication solution.
class WebAuthRepository {
  final WebAuthService _authService;
  final WebSessionService _sessionService;

  const WebAuthRepository(
    this._authService,
    this._sessionService,
  );

  /// Signs in a user with email and password.
  Future<Result<(WebUserModel, AuthSessionModel), AppError>> signInWithEmail(
    String email,
    String password, {
    bool rememberMe = false,
  }) async {
    try {
      // Validate input
      if (email.isEmpty || password.isEmpty) {
        return Result.failure(
          AppError.validation('credentials', 'Email and password are required'),
        );
      }

      // Attempt sign in
      final authResult = await _authService.signInWithEmail(email, password);

      if (authResult.isFailure) {
        return Result.failure(authResult.error!);
      }

      final (user, session) = authResult.data!;

      // Store session in browser
      final storeResult = await _sessionService.storeSession(
        session,
        rememberMe: rememberMe,
      );

      if (storeResult.isFailure) {
        // Sign in succeeded but session storage failed
        // Log the error but don't fail the sign in
        print('Warning: Failed to store session: ${storeResult.error}');
      }

      return Result.success((user, session));
    } catch (e) {
      return Result.failure(
        AppError.unknown('Sign in failed: ${e.toString()}'),
      );
    }
  }

  /// Signs up a new user.
  Future<Result<WebUserModel, AppError>> signUp(
    String email,
    String password,
    String? name,
  ) async {
    try {
      // Validate input
      if (email.isEmpty || password.isEmpty) {
        return Result.failure(
          AppError.validation('credentials', 'Email and password are required'),
        );
      }

      return await _authService.signUp(email, password, name);
    } catch (e) {
      return Result.failure(
        AppError.unknown('Sign up failed: ${e.toString()}'),
      );
    }
  }

  /// Signs out the current user.
  Future<Result<void, AppError>> signOut() async {
    try {
      // Clear local session first
      await _sessionService.clearSession();

      // Then sign out from Supabase
      final result = await _authService.signOut();

      return result;
    } catch (e) {
      return Result.failure(
        AppError.unknown('Sign out failed: ${e.toString()}'),
      );
    }
  }

  /// Sends a password reset email.
  Future<Result<void, AppError>> resetPassword(String email) async {
    try {
      if (email.isEmpty) {
        return Result.failure(
          AppError.validation('email', 'Email is required'),
        );
      }

      return await _authService.resetPassword(email);
    } catch (e) {
      return Result.failure(
        AppError.unknown('Password reset failed: ${e.toString()}'),
      );
    }
  }

  /// Refreshes the current session.
  Future<Result<AuthSessionModel, AppError>> refreshSession() async {
    try {
      final refreshResult = await _authService.refreshSession();

      if (refreshResult.isFailure) {
        return Result.failure(refreshResult.error!);
      }

      final newSession = refreshResult.data!;

      // Update stored session
      final updateResult = await _sessionService.updateSession(newSession);

      if (updateResult.isFailure) {
        // Log the error but don't fail the refresh
        print(
            'Warning: Failed to update stored session: ${updateResult.error}');
      }

      return Result.success(newSession);
    } catch (e) {
      return Result.failure(
        AppError.unknown('Session refresh failed: ${e.toString()}'),
      );
    }
  }

  /// Gets the current user.
  Future<Result<WebUserModel?, AppError>> getCurrentUser() async {
    try {
      return await _authService.getCurrentUser();
    } catch (e) {
      return Result.failure(
        AppError.unknown('Failed to get current user: ${e.toString()}'),
      );
    }
  }

  /// Gets the current session.
  Future<Result<AuthSessionModel?, AppError>> getCurrentSession() async {
    try {
      // First try to get from Supabase
      final supabaseResult = await _authService.getCurrentSession();

      if (supabaseResult.isSuccess && supabaseResult.data != null) {
        return supabaseResult;
      }

      // If not available from Supabase, try stored session
      final storedResult = await _sessionService.getStoredSession();

      if (storedResult.isSuccess && storedResult.data != null) {
        final session = storedResult.data!;

        // Check if stored session is still valid
        if (!session.isExpired) {
          return Result.success(session);
        } else {
          // Clear expired session
          await _sessionService.clearSession();
        }
      }

      return Result.success(null);
    } catch (e) {
      return Result.failure(
        AppError.unknown('Failed to get current session: ${e.toString()}'),
      );
    }
  }

  /// Restores session from browser storage.
  Future<Result<AuthSessionModel?, AppError>> restoreSession() async {
    try {
      return await _sessionService.getStoredSession();
    } catch (e) {
      return Result.failure(
        AppError.unknown('Failed to restore session: ${e.toString()}'),
      );
    }
  }

  /// Updates user profile.
  Future<Result<WebUserModel, AppError>> updateProfile({
    String? name,
    String? avatarUrl,
  }) async {
    try {
      return await _authService.updateProfile(
        name: name,
        avatarUrl: avatarUrl,
      );
    } catch (e) {
      return Result.failure(
        AppError.unknown('Profile update failed: ${e.toString()}'),
      );
    }
  }

  /// Changes user password.
  Future<Result<void, AppError>> changePassword(String newPassword) async {
    try {
      if (newPassword.isEmpty) {
        return Result.failure(
          AppError.validation('password', 'New password is required'),
        );
      }

      return await _authService.changePassword(newPassword);
    } catch (e) {
      return Result.failure(
        AppError.unknown('Password change failed: ${e.toString()}'),
      );
    }
  }

  /// Enables remember me for current session.
  Future<Result<void, AppError>> enableRememberMe() async {
    try {
      return await _sessionService.enableRememberMe();
    } catch (e) {
      return Result.failure(
        AppError.unknown('Failed to enable remember me: ${e.toString()}'),
      );
    }
  }

  /// Disables remember me for current session.
  Future<Result<void, AppError>> disableRememberMe() async {
    try {
      return await _sessionService.disableRememberMe();
    } catch (e) {
      return Result.failure(
        AppError.unknown('Failed to disable remember me: ${e.toString()}'),
      );
    }
  }

  /// Checks if remember me is enabled.
  bool isRememberMeEnabled() {
    return _sessionService.isRememberMeEnabled();
  }

  /// Generates a CSRF token.
  Future<Result<String, AppError>> generateCSRFToken() async {
    try {
      return await _sessionService.generateCSRFToken();
    } catch (e) {
      return Result.failure(
        AppError.unknown('Failed to generate CSRF token: ${e.toString()}'),
      );
    }
  }

  /// Validates a CSRF token.
  bool validateCSRFToken(String token) {
    return _sessionService.validateCSRFToken(token);
  }

  /// Gets authentication state changes stream.
  Stream<dynamic> get authStateChanges => _authService.authStateChanges;

  /// Signs in with OAuth provider.
  Future<Result<(WebUserModel, AuthSessionModel), AppError>> signInWithOAuth({
    required OAuthProvider provider,
    String? redirectTo,
  }) async {
    try {
      return await _authService.signInWithOAuth(
        provider: provider,
        redirectTo: redirectTo,
      );
    } catch (e) {
      return Result.failure(
        AppError.unknown('OAuth sign in failed: ${e.toString()}'),
      );
    }
  }

  /// Handles OAuth callback and completes authentication.
  Future<Result<(WebUserModel, AuthSessionModel), AppError>>
      handleOAuthCallback() async {
    try {
      return await _authService.handleOAuthCallback();
    } catch (e) {
      return Result.failure(
        AppError.unknown('OAuth callback handling failed: ${e.toString()}'),
      );
    }
  }

  /// Gets available OAuth providers.
  List<OAuthProvider> getAvailableOAuthProviders() {
    return _authService.getAvailableOAuthProviders();
  }

  /// Resends email verification for the given email address.
  Future<Result<void, AppError>> resendEmailVerification(String email) async {
    try {
      if (email.trim().isEmpty) {
        return Result.failure(
          AppError.validation('email', 'Email is required'),
        );
      }

      final result = await _authService.resendEmailVerification(email.trim());
      return result;
    } catch (e) {
      return Result.failure(
        AppError.unknown(
            'Failed to resend verification email: ${e.toString()}'),
      );
    }
  }

  /// Verifies email with the provided token.
  Future<Result<void, AppError>> verifyEmail(String token) async {
    try {
      if (token.trim().isEmpty) {
        return Result.failure(
          AppError.validation('token', 'Verification token is required'),
        );
      }

      final result = await _authService.verifyEmail(token.trim());
      return result;
    } catch (e) {
      return Result.failure(
        AppError.unknown('Email verification failed: ${e.toString()}'),
      );
    }
  }

  /// Clears all authentication data.
  Future<Result<void, AppError>> clearAllData() async {
    try {
      await _sessionService.clearAllData();
      return await _authService.signOut();
    } catch (e) {
      return Result.failure(
        AppError.unknown('Failed to clear all data: ${e.toString()}'),
      );
    }
  }
}
