import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:supabase_flutter/supabase_flutter.dart' hide AuthState;

import '../../../../shared/models/result.dart';
import '../../../../shared/models/app_error.dart';
import '../../../../shared/providers/auth_providers.dart' show AuthState;

part 'supabase_service.g.dart';

/// Supabase service provider for web application
@riverpod
SupabaseService supabaseService(SupabaseServiceRef ref) {
  return SupabaseService(Supabase.instance.client);
}

/// Supabase service for web-specific operations
class SupabaseService {
  final SupabaseClient _client;

  SupabaseService(this._client);

  /// Sign in with email and password
  Future<Result<Session, AppError>> signInWithEmail(
      String email, String password) async {
    try {
      final response = await _client.auth.signInWithPassword(
        email: email,
        password: password,
      );

      if (response.session != null) {
        return Result.success(response.session!);
      } else {
        return Result.failure(AppError.authentication('No session returned'));
      }
    } on AuthException catch (e) {
      return Result.failure(AppError.authentication(e.message));
    } catch (e) {
      return Result.failure(AppError.unknown('Sign in failed: $e'));
    }
  }

  /// Sign up with email and password
  Future<Result<Session, AppError>> signUpWithEmail(
      String email, String password) async {
    try {
      final response = await _client.auth.signUp(
        email: email,
        password: password,
      );

      if (response.session != null) {
        return Result.success(response.session!);
      } else {
        return Result.failure(AppError.authentication('No session returned'));
      }
    } on AuthException catch (e) {
      return Result.failure(AppError.authentication(e.message));
    } catch (e) {
      return Result.failure(AppError.unknown('Sign up failed: $e'));
    }
  }

  /// Sign out the current user
  Future<Result<void, AppError>> signOut() async {
    try {
      await _client.auth.signOut();
      return const Result.success(null);
    } on AuthException catch (e) {
      return Result.failure(AppError.authentication(e.message));
    } catch (e) {
      return Result.failure(AppError.unknown('Sign out failed: $e'));
    }
  }

  /// Get the current user
  Future<Result<User?, AppError>> getCurrentUser() async {
    try {
      final user = _client.auth.currentUser;
      return Result.success(user);
    } catch (e) {
      return Result.failure(AppError.unknown('Failed to get current user: $e'));
    }
  }

  /// Get auth state stream
  Stream<AuthState> authStateStream() {
    return _client.auth.onAuthStateChange.map((authState) {
      return AuthState(
        isAuthenticated: authState.session != null,
        user: authState.session?.user,
        isLoading: false,
      );
    });
  }

  /// Subscribe to table changes
  Stream<Map<String, dynamic>> subscribeToTable(
    String tableName,
    void Function(Map<String, dynamic>) callback,
  ) {
    final channel = _client.channel('public:$tableName');

    channel.onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: tableName,
      callback: (payload) {
        final data = payload.newRecord ?? payload.oldRecord ?? {};
        callback(data);
      },
    );

    channel.subscribe();

    // Return a simple stream for testing
    return Stream.periodic(
      const Duration(seconds: 1),
      (_) => {'id': '1', 'updated': DateTime.now().toIso8601String()},
    );
  }

  /// Get Supabase client
  SupabaseClient get client => _client;
}
