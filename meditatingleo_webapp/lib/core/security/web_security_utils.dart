import 'dart:math';
import 'dart:convert';

/// [WebSecurityUtils] provides security utilities for web authentication.
///
/// This class contains static methods for CSRF protection, input sanitization,
/// password validation, and other security-related functionality.
class WebSecurityUtils {
  static String? _currentCSRFToken;
  static final Random _random = Random.secure();

  /// Generates a cryptographically secure CSRF token.
  static String generateCSRFToken() {
    const chars =
        'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final token =
        List.generate(32, (index) => chars[_random.nextInt(chars.length)])
            .join();
    return token;
  }

  /// Sets the current CSRF token for validation.
  static void setCSRFToken(String token) {
    _currentCSRFToken = token;
  }

  /// Validates a CSRF token against the current token.
  static bool validateCSRFToken(String token) {
    if (token.isEmpty || _currentCSRFToken == null) return false;
    return token == _currentCSRFToken;
  }

  /// Sanitizes user input to prevent XSS attacks.
  static String sanitizeInput(String? input) {
    if (input == null || input.isEmpty) return '';

    return input
        .replaceAll('&', '&amp;')
        .replaceAll('<', '&lt;')
        .replaceAll('>', '&gt;')
        .replaceAll('"', '&quot;')
        .replaceAll("'", '&#x27;');
  }

  /// Validates password strength and returns validation result.
  static PasswordValidationResult validatePasswordStrength(String password) {
    final feedback = <String>[];
    int score = 0;

    // Check minimum length
    if (password.length < 8) {
      feedback.add('Password must be at least 8 characters long');
    } else {
      score++;
    }

    // Check for uppercase letter
    if (!RegExp(r'[A-Z]').hasMatch(password)) {
      feedback.add('Password must contain at least one uppercase letter');
    } else {
      score++;
    }

    // Check for lowercase letter
    if (!RegExp(r'[a-z]').hasMatch(password)) {
      feedback.add('Password must contain at least one lowercase letter');
    } else {
      score++;
    }

    // Check for number
    if (!RegExp(r'[0-9]').hasMatch(password)) {
      feedback.add('Password must contain at least one number');
    } else {
      score++;
    }

    // Check for special character
    if (!RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(password)) {
      feedback.add('Password must contain at least one special character');
    } else {
      score++;
    }

    // Additional scoring for length
    if (password.length >= 12) score++;
    if (password.length >= 16) score++;

    return PasswordValidationResult(
      isValid: feedback.isEmpty,
      score: score,
      feedback: feedback,
    );
  }

  /// Validates email format.
  static bool isValidEmail(String email) {
    if (email.isEmpty) return false;

    final emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );

    return emailRegex.hasMatch(email) &&
        !email.contains('..') &&
        !email.startsWith('.') &&
        !email.endsWith('.');
  }

  /// Generates a cryptographically secure random string.
  static String generateSecureRandom(int length) {
    const chars =
        'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    return List.generate(
        length, (index) => chars[_random.nextInt(chars.length)]).join();
  }

  /// Validates if a URL is safe for redirection.
  static bool isSafeURL(String url) {
    if (url.isEmpty) return false;

    try {
      final uri = Uri.parse(url);

      // Only allow HTTPS URLs
      if (uri.scheme != 'https') return false;

      // Reject dangerous schemes
      const dangerousSchemes = ['javascript', 'data', 'file', 'ftp'];
      if (dangerousSchemes.contains(uri.scheme.toLowerCase())) return false;

      // Must have a valid host
      if (uri.host.isEmpty) return false;

      return true;
    } catch (e) {
      return false;
    }
  }

  /// Encodes data for safe storage in browser.
  static String encodeForStorage(String data) {
    return base64Encode(utf8.encode(data));
  }

  /// Decodes data from browser storage.
  static String decodeFromStorage(String encodedData) {
    try {
      return utf8.decode(base64Decode(encodedData));
    } catch (e) {
      return '';
    }
  }

  /// Validates session token format.
  static bool isValidSessionToken(String token) {
    if (token.isEmpty) return false;

    // JWT tokens have 3 parts separated by dots
    final parts = token.split('.');
    if (parts.length != 3) return false;

    // Each part should be base64 encoded
    for (final part in parts) {
      if (part.isEmpty) return false;
      try {
        base64Decode(part + '=='); // Add padding if needed
      } catch (e) {
        return false;
      }
    }

    return true;
  }

  /// Generates a secure session ID.
  static String generateSessionId() {
    return generateSecureRandom(64);
  }

  /// Validates if a string contains only safe characters.
  static bool isSafeString(String input) {
    // Check for dangerous characters that could be used in attacks
    final dangerousChars = [
      '<script',
      'javascript:',
      'data:',
      'vbscript:',
      'onload='
    ];
    final lowerInput = input.toLowerCase();

    for (final dangerous in dangerousChars) {
      if (lowerInput.contains(dangerous)) {
        return false;
      }
    }

    return true;
  }

  /// Clears sensitive data from memory (placeholder for actual implementation).
  static void clearSensitiveData() {
    _currentCSRFToken = null;
  }
}

/// Result of password validation.
class PasswordValidationResult {
  /// Whether the password meets all requirements.
  final bool isValid;

  /// Password strength score (0-7).
  final int score;

  /// List of validation feedback messages.
  final List<String> feedback;

  const PasswordValidationResult({
    required this.isValid,
    required this.score,
    required this.feedback,
  });

  /// Gets the password strength level.
  PasswordStrength get strength {
    if (score <= 2) return PasswordStrength.weak;
    if (score <= 4) return PasswordStrength.medium;
    if (score <= 6) return PasswordStrength.strong;
    return PasswordStrength.veryStrong;
  }

  /// Gets the strength as a percentage.
  double get strengthPercentage => (score / 7).clamp(0.0, 1.0);
}

/// Password strength levels.
enum PasswordStrength {
  weak,
  medium,
  strong,
  veryStrong,
}

extension PasswordStrengthExtension on PasswordStrength {
  String get label {
    switch (this) {
      case PasswordStrength.weak:
        return 'Weak';
      case PasswordStrength.medium:
        return 'Medium';
      case PasswordStrength.strong:
        return 'Strong';
      case PasswordStrength.veryStrong:
        return 'Very Strong';
    }
  }
}
