// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'web_cache_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$webCacheServiceHash() => r'1f9b79012a28dfba14070598d5d179f513456fb1';

/// Web cache service provider
///
/// Copied from [webCacheService].
@ProviderFor(webCacheService)
final webCacheServiceProvider = AutoDisposeProvider<WebCacheService>.internal(
  webCacheService,
  name: r'webCacheServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$webCacheServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef WebCacheServiceRef = AutoDisposeProviderRef<WebCacheService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
