// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'app_providers.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$AppState {
  bool get isInitialized => throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;
  bool get hasError => throw _privateConstructorUsedError;
  String? get errorMessage => throw _privateConstructorUsedError;
  AppThemeMode get themeMode => throw _privateConstructorUsedError;
  String get locale => throw _privateConstructorUsedError;
  DateTime? get lastInitialized => throw _privateConstructorUsedError;

  /// Create a copy of AppState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AppStateCopyWith<AppState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AppStateCopyWith<$Res> {
  factory $AppStateCopyWith(AppState value, $Res Function(AppState) then) =
      _$AppStateCopyWithImpl<$Res, AppState>;
  @useResult
  $Res call(
      {bool isInitialized,
      bool isLoading,
      bool hasError,
      String? errorMessage,
      AppThemeMode themeMode,
      String locale,
      DateTime? lastInitialized});
}

/// @nodoc
class _$AppStateCopyWithImpl<$Res, $Val extends AppState>
    implements $AppStateCopyWith<$Res> {
  _$AppStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AppState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isInitialized = null,
    Object? isLoading = null,
    Object? hasError = null,
    Object? errorMessage = freezed,
    Object? themeMode = null,
    Object? locale = null,
    Object? lastInitialized = freezed,
  }) {
    return _then(_value.copyWith(
      isInitialized: null == isInitialized
          ? _value.isInitialized
          : isInitialized // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      hasError: null == hasError
          ? _value.hasError
          : hasError // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      themeMode: null == themeMode
          ? _value.themeMode
          : themeMode // ignore: cast_nullable_to_non_nullable
              as AppThemeMode,
      locale: null == locale
          ? _value.locale
          : locale // ignore: cast_nullable_to_non_nullable
              as String,
      lastInitialized: freezed == lastInitialized
          ? _value.lastInitialized
          : lastInitialized // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AppStateImplCopyWith<$Res>
    implements $AppStateCopyWith<$Res> {
  factory _$$AppStateImplCopyWith(
          _$AppStateImpl value, $Res Function(_$AppStateImpl) then) =
      __$$AppStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool isInitialized,
      bool isLoading,
      bool hasError,
      String? errorMessage,
      AppThemeMode themeMode,
      String locale,
      DateTime? lastInitialized});
}

/// @nodoc
class __$$AppStateImplCopyWithImpl<$Res>
    extends _$AppStateCopyWithImpl<$Res, _$AppStateImpl>
    implements _$$AppStateImplCopyWith<$Res> {
  __$$AppStateImplCopyWithImpl(
      _$AppStateImpl _value, $Res Function(_$AppStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of AppState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isInitialized = null,
    Object? isLoading = null,
    Object? hasError = null,
    Object? errorMessage = freezed,
    Object? themeMode = null,
    Object? locale = null,
    Object? lastInitialized = freezed,
  }) {
    return _then(_$AppStateImpl(
      isInitialized: null == isInitialized
          ? _value.isInitialized
          : isInitialized // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      hasError: null == hasError
          ? _value.hasError
          : hasError // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      themeMode: null == themeMode
          ? _value.themeMode
          : themeMode // ignore: cast_nullable_to_non_nullable
              as AppThemeMode,
      locale: null == locale
          ? _value.locale
          : locale // ignore: cast_nullable_to_non_nullable
              as String,
      lastInitialized: freezed == lastInitialized
          ? _value.lastInitialized
          : lastInitialized // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc

class _$AppStateImpl implements _AppState {
  const _$AppStateImpl(
      {this.isInitialized = false,
      this.isLoading = false,
      this.hasError = false,
      this.errorMessage,
      this.themeMode = AppThemeMode.system,
      this.locale = 'en',
      this.lastInitialized});

  @override
  @JsonKey()
  final bool isInitialized;
  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final bool hasError;
  @override
  final String? errorMessage;
  @override
  @JsonKey()
  final AppThemeMode themeMode;
  @override
  @JsonKey()
  final String locale;
  @override
  final DateTime? lastInitialized;

  @override
  String toString() {
    return 'AppState(isInitialized: $isInitialized, isLoading: $isLoading, hasError: $hasError, errorMessage: $errorMessage, themeMode: $themeMode, locale: $locale, lastInitialized: $lastInitialized)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AppStateImpl &&
            (identical(other.isInitialized, isInitialized) ||
                other.isInitialized == isInitialized) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.hasError, hasError) ||
                other.hasError == hasError) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            (identical(other.themeMode, themeMode) ||
                other.themeMode == themeMode) &&
            (identical(other.locale, locale) || other.locale == locale) &&
            (identical(other.lastInitialized, lastInitialized) ||
                other.lastInitialized == lastInitialized));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isInitialized, isLoading,
      hasError, errorMessage, themeMode, locale, lastInitialized);

  /// Create a copy of AppState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AppStateImplCopyWith<_$AppStateImpl> get copyWith =>
      __$$AppStateImplCopyWithImpl<_$AppStateImpl>(this, _$identity);
}

abstract class _AppState implements AppState {
  const factory _AppState(
      {final bool isInitialized,
      final bool isLoading,
      final bool hasError,
      final String? errorMessage,
      final AppThemeMode themeMode,
      final String locale,
      final DateTime? lastInitialized}) = _$AppStateImpl;

  @override
  bool get isInitialized;
  @override
  bool get isLoading;
  @override
  bool get hasError;
  @override
  String? get errorMessage;
  @override
  AppThemeMode get themeMode;
  @override
  String get locale;
  @override
  DateTime? get lastInitialized;

  /// Create a copy of AppState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AppStateImplCopyWith<_$AppStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$NavigationState {
  String get currentRoute => throw _privateConstructorUsedError;
  List<String> get history => throw _privateConstructorUsedError;
  bool get canGoBack => throw _privateConstructorUsedError;

  /// Create a copy of NavigationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $NavigationStateCopyWith<NavigationState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $NavigationStateCopyWith<$Res> {
  factory $NavigationStateCopyWith(
          NavigationState value, $Res Function(NavigationState) then) =
      _$NavigationStateCopyWithImpl<$Res, NavigationState>;
  @useResult
  $Res call({String currentRoute, List<String> history, bool canGoBack});
}

/// @nodoc
class _$NavigationStateCopyWithImpl<$Res, $Val extends NavigationState>
    implements $NavigationStateCopyWith<$Res> {
  _$NavigationStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of NavigationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentRoute = null,
    Object? history = null,
    Object? canGoBack = null,
  }) {
    return _then(_value.copyWith(
      currentRoute: null == currentRoute
          ? _value.currentRoute
          : currentRoute // ignore: cast_nullable_to_non_nullable
              as String,
      history: null == history
          ? _value.history
          : history // ignore: cast_nullable_to_non_nullable
              as List<String>,
      canGoBack: null == canGoBack
          ? _value.canGoBack
          : canGoBack // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$NavigationStateImplCopyWith<$Res>
    implements $NavigationStateCopyWith<$Res> {
  factory _$$NavigationStateImplCopyWith(_$NavigationStateImpl value,
          $Res Function(_$NavigationStateImpl) then) =
      __$$NavigationStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String currentRoute, List<String> history, bool canGoBack});
}

/// @nodoc
class __$$NavigationStateImplCopyWithImpl<$Res>
    extends _$NavigationStateCopyWithImpl<$Res, _$NavigationStateImpl>
    implements _$$NavigationStateImplCopyWith<$Res> {
  __$$NavigationStateImplCopyWithImpl(
      _$NavigationStateImpl _value, $Res Function(_$NavigationStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of NavigationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentRoute = null,
    Object? history = null,
    Object? canGoBack = null,
  }) {
    return _then(_$NavigationStateImpl(
      currentRoute: null == currentRoute
          ? _value.currentRoute
          : currentRoute // ignore: cast_nullable_to_non_nullable
              as String,
      history: null == history
          ? _value._history
          : history // ignore: cast_nullable_to_non_nullable
              as List<String>,
      canGoBack: null == canGoBack
          ? _value.canGoBack
          : canGoBack // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$NavigationStateImpl implements _NavigationState {
  const _$NavigationStateImpl(
      {this.currentRoute = '/',
      final List<String> history = const ['/'],
      this.canGoBack = false})
      : _history = history;

  @override
  @JsonKey()
  final String currentRoute;
  final List<String> _history;
  @override
  @JsonKey()
  List<String> get history {
    if (_history is EqualUnmodifiableListView) return _history;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_history);
  }

  @override
  @JsonKey()
  final bool canGoBack;

  @override
  String toString() {
    return 'NavigationState(currentRoute: $currentRoute, history: $history, canGoBack: $canGoBack)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NavigationStateImpl &&
            (identical(other.currentRoute, currentRoute) ||
                other.currentRoute == currentRoute) &&
            const DeepCollectionEquality().equals(other._history, _history) &&
            (identical(other.canGoBack, canGoBack) ||
                other.canGoBack == canGoBack));
  }

  @override
  int get hashCode => Object.hash(runtimeType, currentRoute,
      const DeepCollectionEquality().hash(_history), canGoBack);

  /// Create a copy of NavigationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$NavigationStateImplCopyWith<_$NavigationStateImpl> get copyWith =>
      __$$NavigationStateImplCopyWithImpl<_$NavigationStateImpl>(
          this, _$identity);
}

abstract class _NavigationState implements NavigationState {
  const factory _NavigationState(
      {final String currentRoute,
      final List<String> history,
      final bool canGoBack}) = _$NavigationStateImpl;

  @override
  String get currentRoute;
  @override
  List<String> get history;
  @override
  bool get canGoBack;

  /// Create a copy of NavigationState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$NavigationStateImplCopyWith<_$NavigationStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$RealTimeState {
  bool get isConnected => throw _privateConstructorUsedError;
  Map<String, String> get activeSubscriptions =>
      throw _privateConstructorUsedError;
  DateTime? get lastUpdate => throw _privateConstructorUsedError;
  DateTime? get lastConnectionCheck => throw _privateConstructorUsedError;

  /// Create a copy of RealTimeState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $RealTimeStateCopyWith<RealTimeState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RealTimeStateCopyWith<$Res> {
  factory $RealTimeStateCopyWith(
          RealTimeState value, $Res Function(RealTimeState) then) =
      _$RealTimeStateCopyWithImpl<$Res, RealTimeState>;
  @useResult
  $Res call(
      {bool isConnected,
      Map<String, String> activeSubscriptions,
      DateTime? lastUpdate,
      DateTime? lastConnectionCheck});
}

/// @nodoc
class _$RealTimeStateCopyWithImpl<$Res, $Val extends RealTimeState>
    implements $RealTimeStateCopyWith<$Res> {
  _$RealTimeStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of RealTimeState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isConnected = null,
    Object? activeSubscriptions = null,
    Object? lastUpdate = freezed,
    Object? lastConnectionCheck = freezed,
  }) {
    return _then(_value.copyWith(
      isConnected: null == isConnected
          ? _value.isConnected
          : isConnected // ignore: cast_nullable_to_non_nullable
              as bool,
      activeSubscriptions: null == activeSubscriptions
          ? _value.activeSubscriptions
          : activeSubscriptions // ignore: cast_nullable_to_non_nullable
              as Map<String, String>,
      lastUpdate: freezed == lastUpdate
          ? _value.lastUpdate
          : lastUpdate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      lastConnectionCheck: freezed == lastConnectionCheck
          ? _value.lastConnectionCheck
          : lastConnectionCheck // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$RealTimeStateImplCopyWith<$Res>
    implements $RealTimeStateCopyWith<$Res> {
  factory _$$RealTimeStateImplCopyWith(
          _$RealTimeStateImpl value, $Res Function(_$RealTimeStateImpl) then) =
      __$$RealTimeStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool isConnected,
      Map<String, String> activeSubscriptions,
      DateTime? lastUpdate,
      DateTime? lastConnectionCheck});
}

/// @nodoc
class __$$RealTimeStateImplCopyWithImpl<$Res>
    extends _$RealTimeStateCopyWithImpl<$Res, _$RealTimeStateImpl>
    implements _$$RealTimeStateImplCopyWith<$Res> {
  __$$RealTimeStateImplCopyWithImpl(
      _$RealTimeStateImpl _value, $Res Function(_$RealTimeStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of RealTimeState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isConnected = null,
    Object? activeSubscriptions = null,
    Object? lastUpdate = freezed,
    Object? lastConnectionCheck = freezed,
  }) {
    return _then(_$RealTimeStateImpl(
      isConnected: null == isConnected
          ? _value.isConnected
          : isConnected // ignore: cast_nullable_to_non_nullable
              as bool,
      activeSubscriptions: null == activeSubscriptions
          ? _value._activeSubscriptions
          : activeSubscriptions // ignore: cast_nullable_to_non_nullable
              as Map<String, String>,
      lastUpdate: freezed == lastUpdate
          ? _value.lastUpdate
          : lastUpdate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      lastConnectionCheck: freezed == lastConnectionCheck
          ? _value.lastConnectionCheck
          : lastConnectionCheck // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc

class _$RealTimeStateImpl implements _RealTimeState {
  const _$RealTimeStateImpl(
      {this.isConnected = false,
      final Map<String, String> activeSubscriptions = const {},
      this.lastUpdate,
      this.lastConnectionCheck})
      : _activeSubscriptions = activeSubscriptions;

  @override
  @JsonKey()
  final bool isConnected;
  final Map<String, String> _activeSubscriptions;
  @override
  @JsonKey()
  Map<String, String> get activeSubscriptions {
    if (_activeSubscriptions is EqualUnmodifiableMapView)
      return _activeSubscriptions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_activeSubscriptions);
  }

  @override
  final DateTime? lastUpdate;
  @override
  final DateTime? lastConnectionCheck;

  @override
  String toString() {
    return 'RealTimeState(isConnected: $isConnected, activeSubscriptions: $activeSubscriptions, lastUpdate: $lastUpdate, lastConnectionCheck: $lastConnectionCheck)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RealTimeStateImpl &&
            (identical(other.isConnected, isConnected) ||
                other.isConnected == isConnected) &&
            const DeepCollectionEquality()
                .equals(other._activeSubscriptions, _activeSubscriptions) &&
            (identical(other.lastUpdate, lastUpdate) ||
                other.lastUpdate == lastUpdate) &&
            (identical(other.lastConnectionCheck, lastConnectionCheck) ||
                other.lastConnectionCheck == lastConnectionCheck));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      isConnected,
      const DeepCollectionEquality().hash(_activeSubscriptions),
      lastUpdate,
      lastConnectionCheck);

  /// Create a copy of RealTimeState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RealTimeStateImplCopyWith<_$RealTimeStateImpl> get copyWith =>
      __$$RealTimeStateImplCopyWithImpl<_$RealTimeStateImpl>(this, _$identity);
}

abstract class _RealTimeState implements RealTimeState {
  const factory _RealTimeState(
      {final bool isConnected,
      final Map<String, String> activeSubscriptions,
      final DateTime? lastUpdate,
      final DateTime? lastConnectionCheck}) = _$RealTimeStateImpl;

  @override
  bool get isConnected;
  @override
  Map<String, String> get activeSubscriptions;
  @override
  DateTime? get lastUpdate;
  @override
  DateTime? get lastConnectionCheck;

  /// Create a copy of RealTimeState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RealTimeStateImplCopyWith<_$RealTimeStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
