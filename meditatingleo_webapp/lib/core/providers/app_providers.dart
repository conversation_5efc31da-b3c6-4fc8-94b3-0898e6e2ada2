import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../state/web_state_manager.dart';
import '../services/web_cache_service.dart';
import '../../features/database/data/services/supabase_service.dart';
import '../../shared/providers/auth_providers.dart';
import '../../shared/providers/cache_providers.dart';

part 'app_providers.freezed.dart';
part 'app_providers.g.dart';

/// Global app state notifier
@riverpod
class AppNotifier extends _$AppNotifier {
  @override
  AsyncValue<AppState> build() {
    return const AsyncValue.data(AppState());
  }

  /// Initialize the application
  Future<void> initialize() async {
    state = const AsyncValue.loading();
    
    try {
      // Initialize web state manager
      final webStateManager = ref.read(webStateManagerProvider.notifier);
      await webStateManager.initialize();
      
      // Initialize cache service
      final cacheService = ref.read(webCacheServiceProvider);
      await cacheService.initialize();
      
      // Get current user if any
      final authNotifier = ref.read(authNotifierProvider.notifier);
      await authNotifier.getCurrentUser();
      
      state = AsyncValue.data(
        const AppState(
          isInitialized: true,
          isLoading: false,
        ),
      );
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Set app theme mode
  void setThemeMode(AppThemeMode themeMode) {
    final currentState = state.value ?? const AppState();
    state = AsyncValue.data(
      currentState.copyWith(themeMode: themeMode),
    );
  }

  /// Set app locale
  void setLocale(String locale) {
    final currentState = state.value ?? const AppState();
    state = AsyncValue.data(
      currentState.copyWith(locale: locale),
    );
  }

  /// Set loading state
  void setLoading(bool isLoading) {
    final currentState = state.value ?? const AppState();
    state = AsyncValue.data(
      currentState.copyWith(isLoading: isLoading),
    );
  }

  /// Handle app error
  void handleError(String errorMessage) {
    final currentState = state.value ?? const AppState();
    state = AsyncValue.data(
      currentState.copyWith(
        errorMessage: errorMessage,
        hasError: true,
      ),
    );
  }

  /// Clear app error
  void clearError() {
    final currentState = state.value ?? const AppState();
    state = AsyncValue.data(
      currentState.copyWith(
        errorMessage: null,
        hasError: false,
      ),
    );
  }

  /// Cleanup app resources
  Future<void> cleanup() async {
    try {
      // Cleanup web state manager
      final webStateManager = ref.read(webStateManagerProvider.notifier);
      await webStateManager.cleanup();
      
      // Clear cache if needed
      final cacheNotifier = ref.read(webCacheNotifierProvider.notifier);
      await cacheNotifier.clearCache();
      
      state = const AsyncValue.data(AppState());
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

/// Navigation state notifier
@riverpod
class NavigationNotifier extends _$NavigationNotifier {
  @override
  NavigationState build() {
    return const NavigationState();
  }

  /// Set current route
  void setCurrentRoute(String route) {
    state = state.copyWith(currentRoute: route);
  }

  /// Set navigation history
  void addToHistory(String route) {
    final updatedHistory = List<String>.from(state.history)..add(route);
    state = state.copyWith(
      history: updatedHistory,
      currentRoute: route,
    );
  }

  /// Go back in navigation
  void goBack() {
    if (state.history.length > 1) {
      final updatedHistory = List<String>.from(state.history)..removeLast();
      final previousRoute = updatedHistory.last;
      state = state.copyWith(
        history: updatedHistory,
        currentRoute: previousRoute,
      );
    }
  }

  /// Clear navigation history
  void clearHistory() {
    state = state.copyWith(
      history: [state.currentRoute],
    );
  }

  /// Check if can go back
  bool canGoBack() {
    return state.history.length > 1;
  }
}

/// Real-time connection state notifier
@riverpod
class RealTimeNotifier extends _$RealTimeNotifier {
  @override
  RealTimeState build() {
    return const RealTimeState();
  }

  /// Set connection status
  void setConnectionStatus(bool isConnected) {
    state = state.copyWith(
      isConnected: isConnected,
      lastConnectionCheck: DateTime.now(),
    );
  }

  /// Add active subscription
  void addSubscription(String key, String tableName) {
    final updatedSubscriptions = Map<String, String>.from(state.activeSubscriptions);
    updatedSubscriptions[key] = tableName;
    
    state = state.copyWith(
      activeSubscriptions: updatedSubscriptions,
    );
  }

  /// Remove subscription
  void removeSubscription(String key) {
    final updatedSubscriptions = Map<String, String>.from(state.activeSubscriptions);
    updatedSubscriptions.remove(key);
    
    state = state.copyWith(
      activeSubscriptions: updatedSubscriptions,
    );
  }

  /// Set last update time
  void setLastUpdate(DateTime lastUpdate) {
    state = state.copyWith(lastUpdate: lastUpdate);
  }

  /// Check connection health
  Future<void> checkConnection() async {
    try {
      final supabaseService = ref.read(supabaseServiceProvider);
      // TODO: Implement actual connection check
      setConnectionStatus(true);
    } catch (e) {
      setConnectionStatus(false);
    }
  }
}

/// App state model
@freezed
class AppState with _$AppState {
  const factory AppState({
    @Default(false) bool isInitialized,
    @Default(false) bool isLoading,
    @Default(false) bool hasError,
    String? errorMessage,
    @Default(AppThemeMode.system) AppThemeMode themeMode,
    @Default('en') String locale,
    DateTime? lastInitialized,
  }) = _AppState;
}

/// Navigation state model
@freezed
class NavigationState with _$NavigationState {
  const factory NavigationState({
    @Default('/') String currentRoute,
    @Default(['/']) List<String> history,
    @Default(false) bool canGoBack,
  }) = _NavigationState;
}

/// Real-time connection state model
@freezed
class RealTimeState with _$RealTimeState {
  const factory RealTimeState({
    @Default(false) bool isConnected,
    @Default({}) Map<String, String> activeSubscriptions,
    DateTime? lastUpdate,
    DateTime? lastConnectionCheck,
  }) = _RealTimeState;
}

/// App theme mode enum
enum AppThemeMode {
  light,
  dark,
  system,
}
