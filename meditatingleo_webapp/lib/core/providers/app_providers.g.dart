// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$appNotifierHash() => r'aadf12dc55f57723f070641eb9445f5fcc0d22de';

/// Global app state notifier
///
/// Copied from [AppNotifier].
@ProviderFor(AppNotifier)
final appNotifierProvider =
    AutoDisposeNotifierProvider<AppNotifier, AsyncValue<AppState>>.internal(
  AppNotifier.new,
  name: r'appNotifierProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$appNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$AppNotifier = AutoDisposeNotifier<AsyncValue<AppState>>;
String _$navigationNotifierHash() =>
    r'719f285b18aa17e214faf68eac831b28c30bd4f4';

/// Navigation state notifier
///
/// Copied from [NavigationNotifier].
@ProviderFor(NavigationNotifier)
final navigationNotifierProvider =
    AutoDisposeNotifierProvider<NavigationNotifier, NavigationState>.internal(
  NavigationNotifier.new,
  name: r'navigationNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$navigationNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$NavigationNotifier = AutoDisposeNotifier<NavigationState>;
String _$realTimeNotifierHash() => r'fe22dbe5377cc514a9e27691716bf397bc256775';

/// Real-time connection state notifier
///
/// Copied from [RealTimeNotifier].
@ProviderFor(RealTimeNotifier)
final realTimeNotifierProvider =
    AutoDisposeNotifierProvider<RealTimeNotifier, RealTimeState>.internal(
  RealTimeNotifier.new,
  name: r'realTimeNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$realTimeNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$RealTimeNotifier = AutoDisposeNotifier<RealTimeState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
