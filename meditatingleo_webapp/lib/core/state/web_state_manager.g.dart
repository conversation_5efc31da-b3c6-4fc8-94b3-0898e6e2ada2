// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'web_state_manager.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$webStateManagerHash() => r'1cbc4faeb0bf51a9dbf211a5b2f130d3f901ef64';

/// Web-specific state manager for browser optimizations and desktop workflows
///
/// Copied from [WebStateManager].
@ProviderFor(WebStateManager)
final webStateManagerProvider = AutoDisposeNotifierProvider<WebStateManager,
    AsyncValue<WebStateManagerState>>.internal(
  WebStateManager.new,
  name: r'webStateManagerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$webStateManagerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$WebStateManager
    = AutoDisposeNotifier<AsyncValue<WebStateManagerState>>;
String _$desktopLayoutNotifierHash() =>
    r'22199e7079d5ce12a8d325ef433b8d2d62231fcb';

/// Desktop layout state provider
///
/// Copied from [DesktopLayoutNotifier].
@ProviderFor(DesktopLayoutNotifier)
final desktopLayoutNotifierProvider = AutoDisposeNotifierProvider<
    DesktopLayoutNotifier, DesktopLayoutState>.internal(
  DesktopLayoutNotifier.new,
  name: r'desktopLayoutNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$desktopLayoutNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$DesktopLayoutNotifier = AutoDisposeNotifier<DesktopLayoutState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
