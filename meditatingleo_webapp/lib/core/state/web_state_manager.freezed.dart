// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'web_state_manager.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$WebStateManagerState {
  bool get isInitialized => throw _privateConstructorUsedError;
  Set<String> get activeSubscriptions => throw _privateConstructorUsedError;
  DesktopLayoutState get desktopLayout => throw _privateConstructorUsedError;
  BulkOperationsState get bulkOperations => throw _privateConstructorUsedError;

  /// Create a copy of WebStateManagerState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $WebStateManagerStateCopyWith<WebStateManagerState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WebStateManagerStateCopyWith<$Res> {
  factory $WebStateManagerStateCopyWith(WebStateManagerState value,
          $Res Function(WebStateManagerState) then) =
      _$WebStateManagerStateCopyWithImpl<$Res, WebStateManagerState>;
  @useResult
  $Res call(
      {bool isInitialized,
      Set<String> activeSubscriptions,
      DesktopLayoutState desktopLayout,
      BulkOperationsState bulkOperations});

  $DesktopLayoutStateCopyWith<$Res> get desktopLayout;
  $BulkOperationsStateCopyWith<$Res> get bulkOperations;
}

/// @nodoc
class _$WebStateManagerStateCopyWithImpl<$Res,
        $Val extends WebStateManagerState>
    implements $WebStateManagerStateCopyWith<$Res> {
  _$WebStateManagerStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of WebStateManagerState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isInitialized = null,
    Object? activeSubscriptions = null,
    Object? desktopLayout = null,
    Object? bulkOperations = null,
  }) {
    return _then(_value.copyWith(
      isInitialized: null == isInitialized
          ? _value.isInitialized
          : isInitialized // ignore: cast_nullable_to_non_nullable
              as bool,
      activeSubscriptions: null == activeSubscriptions
          ? _value.activeSubscriptions
          : activeSubscriptions // ignore: cast_nullable_to_non_nullable
              as Set<String>,
      desktopLayout: null == desktopLayout
          ? _value.desktopLayout
          : desktopLayout // ignore: cast_nullable_to_non_nullable
              as DesktopLayoutState,
      bulkOperations: null == bulkOperations
          ? _value.bulkOperations
          : bulkOperations // ignore: cast_nullable_to_non_nullable
              as BulkOperationsState,
    ) as $Val);
  }

  /// Create a copy of WebStateManagerState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $DesktopLayoutStateCopyWith<$Res> get desktopLayout {
    return $DesktopLayoutStateCopyWith<$Res>(_value.desktopLayout, (value) {
      return _then(_value.copyWith(desktopLayout: value) as $Val);
    });
  }

  /// Create a copy of WebStateManagerState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $BulkOperationsStateCopyWith<$Res> get bulkOperations {
    return $BulkOperationsStateCopyWith<$Res>(_value.bulkOperations, (value) {
      return _then(_value.copyWith(bulkOperations: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$WebStateManagerStateImplCopyWith<$Res>
    implements $WebStateManagerStateCopyWith<$Res> {
  factory _$$WebStateManagerStateImplCopyWith(_$WebStateManagerStateImpl value,
          $Res Function(_$WebStateManagerStateImpl) then) =
      __$$WebStateManagerStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool isInitialized,
      Set<String> activeSubscriptions,
      DesktopLayoutState desktopLayout,
      BulkOperationsState bulkOperations});

  @override
  $DesktopLayoutStateCopyWith<$Res> get desktopLayout;
  @override
  $BulkOperationsStateCopyWith<$Res> get bulkOperations;
}

/// @nodoc
class __$$WebStateManagerStateImplCopyWithImpl<$Res>
    extends _$WebStateManagerStateCopyWithImpl<$Res, _$WebStateManagerStateImpl>
    implements _$$WebStateManagerStateImplCopyWith<$Res> {
  __$$WebStateManagerStateImplCopyWithImpl(_$WebStateManagerStateImpl _value,
      $Res Function(_$WebStateManagerStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of WebStateManagerState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isInitialized = null,
    Object? activeSubscriptions = null,
    Object? desktopLayout = null,
    Object? bulkOperations = null,
  }) {
    return _then(_$WebStateManagerStateImpl(
      isInitialized: null == isInitialized
          ? _value.isInitialized
          : isInitialized // ignore: cast_nullable_to_non_nullable
              as bool,
      activeSubscriptions: null == activeSubscriptions
          ? _value._activeSubscriptions
          : activeSubscriptions // ignore: cast_nullable_to_non_nullable
              as Set<String>,
      desktopLayout: null == desktopLayout
          ? _value.desktopLayout
          : desktopLayout // ignore: cast_nullable_to_non_nullable
              as DesktopLayoutState,
      bulkOperations: null == bulkOperations
          ? _value.bulkOperations
          : bulkOperations // ignore: cast_nullable_to_non_nullable
              as BulkOperationsState,
    ));
  }
}

/// @nodoc

class _$WebStateManagerStateImpl implements _WebStateManagerState {
  const _$WebStateManagerStateImpl(
      {this.isInitialized = false,
      final Set<String> activeSubscriptions = const {},
      this.desktopLayout = const DesktopLayoutState(),
      this.bulkOperations = const BulkOperationsState()})
      : _activeSubscriptions = activeSubscriptions;

  @override
  @JsonKey()
  final bool isInitialized;
  final Set<String> _activeSubscriptions;
  @override
  @JsonKey()
  Set<String> get activeSubscriptions {
    if (_activeSubscriptions is EqualUnmodifiableSetView)
      return _activeSubscriptions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableSetView(_activeSubscriptions);
  }

  @override
  @JsonKey()
  final DesktopLayoutState desktopLayout;
  @override
  @JsonKey()
  final BulkOperationsState bulkOperations;

  @override
  String toString() {
    return 'WebStateManagerState(isInitialized: $isInitialized, activeSubscriptions: $activeSubscriptions, desktopLayout: $desktopLayout, bulkOperations: $bulkOperations)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WebStateManagerStateImpl &&
            (identical(other.isInitialized, isInitialized) ||
                other.isInitialized == isInitialized) &&
            const DeepCollectionEquality()
                .equals(other._activeSubscriptions, _activeSubscriptions) &&
            (identical(other.desktopLayout, desktopLayout) ||
                other.desktopLayout == desktopLayout) &&
            (identical(other.bulkOperations, bulkOperations) ||
                other.bulkOperations == bulkOperations));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      isInitialized,
      const DeepCollectionEquality().hash(_activeSubscriptions),
      desktopLayout,
      bulkOperations);

  /// Create a copy of WebStateManagerState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$WebStateManagerStateImplCopyWith<_$WebStateManagerStateImpl>
      get copyWith =>
          __$$WebStateManagerStateImplCopyWithImpl<_$WebStateManagerStateImpl>(
              this, _$identity);
}

abstract class _WebStateManagerState implements WebStateManagerState {
  const factory _WebStateManagerState(
      {final bool isInitialized,
      final Set<String> activeSubscriptions,
      final DesktopLayoutState desktopLayout,
      final BulkOperationsState bulkOperations}) = _$WebStateManagerStateImpl;

  @override
  bool get isInitialized;
  @override
  Set<String> get activeSubscriptions;
  @override
  DesktopLayoutState get desktopLayout;
  @override
  BulkOperationsState get bulkOperations;

  /// Create a copy of WebStateManagerState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$WebStateManagerStateImplCopyWith<_$WebStateManagerStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$DesktopLayoutState {
  bool get leftPanelOpen => throw _privateConstructorUsedError;
  bool get rightPanelOpen => throw _privateConstructorUsedError;
  double get leftPanelWidth => throw _privateConstructorUsedError;
  double get rightPanelWidth => throw _privateConstructorUsedError;

  /// Create a copy of DesktopLayoutState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DesktopLayoutStateCopyWith<DesktopLayoutState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DesktopLayoutStateCopyWith<$Res> {
  factory $DesktopLayoutStateCopyWith(
          DesktopLayoutState value, $Res Function(DesktopLayoutState) then) =
      _$DesktopLayoutStateCopyWithImpl<$Res, DesktopLayoutState>;
  @useResult
  $Res call(
      {bool leftPanelOpen,
      bool rightPanelOpen,
      double leftPanelWidth,
      double rightPanelWidth});
}

/// @nodoc
class _$DesktopLayoutStateCopyWithImpl<$Res, $Val extends DesktopLayoutState>
    implements $DesktopLayoutStateCopyWith<$Res> {
  _$DesktopLayoutStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DesktopLayoutState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? leftPanelOpen = null,
    Object? rightPanelOpen = null,
    Object? leftPanelWidth = null,
    Object? rightPanelWidth = null,
  }) {
    return _then(_value.copyWith(
      leftPanelOpen: null == leftPanelOpen
          ? _value.leftPanelOpen
          : leftPanelOpen // ignore: cast_nullable_to_non_nullable
              as bool,
      rightPanelOpen: null == rightPanelOpen
          ? _value.rightPanelOpen
          : rightPanelOpen // ignore: cast_nullable_to_non_nullable
              as bool,
      leftPanelWidth: null == leftPanelWidth
          ? _value.leftPanelWidth
          : leftPanelWidth // ignore: cast_nullable_to_non_nullable
              as double,
      rightPanelWidth: null == rightPanelWidth
          ? _value.rightPanelWidth
          : rightPanelWidth // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DesktopLayoutStateImplCopyWith<$Res>
    implements $DesktopLayoutStateCopyWith<$Res> {
  factory _$$DesktopLayoutStateImplCopyWith(_$DesktopLayoutStateImpl value,
          $Res Function(_$DesktopLayoutStateImpl) then) =
      __$$DesktopLayoutStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool leftPanelOpen,
      bool rightPanelOpen,
      double leftPanelWidth,
      double rightPanelWidth});
}

/// @nodoc
class __$$DesktopLayoutStateImplCopyWithImpl<$Res>
    extends _$DesktopLayoutStateCopyWithImpl<$Res, _$DesktopLayoutStateImpl>
    implements _$$DesktopLayoutStateImplCopyWith<$Res> {
  __$$DesktopLayoutStateImplCopyWithImpl(_$DesktopLayoutStateImpl _value,
      $Res Function(_$DesktopLayoutStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of DesktopLayoutState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? leftPanelOpen = null,
    Object? rightPanelOpen = null,
    Object? leftPanelWidth = null,
    Object? rightPanelWidth = null,
  }) {
    return _then(_$DesktopLayoutStateImpl(
      leftPanelOpen: null == leftPanelOpen
          ? _value.leftPanelOpen
          : leftPanelOpen // ignore: cast_nullable_to_non_nullable
              as bool,
      rightPanelOpen: null == rightPanelOpen
          ? _value.rightPanelOpen
          : rightPanelOpen // ignore: cast_nullable_to_non_nullable
              as bool,
      leftPanelWidth: null == leftPanelWidth
          ? _value.leftPanelWidth
          : leftPanelWidth // ignore: cast_nullable_to_non_nullable
              as double,
      rightPanelWidth: null == rightPanelWidth
          ? _value.rightPanelWidth
          : rightPanelWidth // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc

class _$DesktopLayoutStateImpl implements _DesktopLayoutState {
  const _$DesktopLayoutStateImpl(
      {this.leftPanelOpen = false,
      this.rightPanelOpen = false,
      this.leftPanelWidth = 280.0,
      this.rightPanelWidth = 320.0});

  @override
  @JsonKey()
  final bool leftPanelOpen;
  @override
  @JsonKey()
  final bool rightPanelOpen;
  @override
  @JsonKey()
  final double leftPanelWidth;
  @override
  @JsonKey()
  final double rightPanelWidth;

  @override
  String toString() {
    return 'DesktopLayoutState(leftPanelOpen: $leftPanelOpen, rightPanelOpen: $rightPanelOpen, leftPanelWidth: $leftPanelWidth, rightPanelWidth: $rightPanelWidth)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DesktopLayoutStateImpl &&
            (identical(other.leftPanelOpen, leftPanelOpen) ||
                other.leftPanelOpen == leftPanelOpen) &&
            (identical(other.rightPanelOpen, rightPanelOpen) ||
                other.rightPanelOpen == rightPanelOpen) &&
            (identical(other.leftPanelWidth, leftPanelWidth) ||
                other.leftPanelWidth == leftPanelWidth) &&
            (identical(other.rightPanelWidth, rightPanelWidth) ||
                other.rightPanelWidth == rightPanelWidth));
  }

  @override
  int get hashCode => Object.hash(runtimeType, leftPanelOpen, rightPanelOpen,
      leftPanelWidth, rightPanelWidth);

  /// Create a copy of DesktopLayoutState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DesktopLayoutStateImplCopyWith<_$DesktopLayoutStateImpl> get copyWith =>
      __$$DesktopLayoutStateImplCopyWithImpl<_$DesktopLayoutStateImpl>(
          this, _$identity);
}

abstract class _DesktopLayoutState implements DesktopLayoutState {
  const factory _DesktopLayoutState(
      {final bool leftPanelOpen,
      final bool rightPanelOpen,
      final double leftPanelWidth,
      final double rightPanelWidth}) = _$DesktopLayoutStateImpl;

  @override
  bool get leftPanelOpen;
  @override
  bool get rightPanelOpen;
  @override
  double get leftPanelWidth;
  @override
  double get rightPanelWidth;

  /// Create a copy of DesktopLayoutState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DesktopLayoutStateImplCopyWith<_$DesktopLayoutStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$BulkOperationsState {
  bool get isActive => throw _privateConstructorUsedError;
  Set<String> get selectedItems => throw _privateConstructorUsedError;

  /// Create a copy of BulkOperationsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BulkOperationsStateCopyWith<BulkOperationsState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BulkOperationsStateCopyWith<$Res> {
  factory $BulkOperationsStateCopyWith(
          BulkOperationsState value, $Res Function(BulkOperationsState) then) =
      _$BulkOperationsStateCopyWithImpl<$Res, BulkOperationsState>;
  @useResult
  $Res call({bool isActive, Set<String> selectedItems});
}

/// @nodoc
class _$BulkOperationsStateCopyWithImpl<$Res, $Val extends BulkOperationsState>
    implements $BulkOperationsStateCopyWith<$Res> {
  _$BulkOperationsStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BulkOperationsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isActive = null,
    Object? selectedItems = null,
  }) {
    return _then(_value.copyWith(
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
      selectedItems: null == selectedItems
          ? _value.selectedItems
          : selectedItems // ignore: cast_nullable_to_non_nullable
              as Set<String>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$BulkOperationsStateImplCopyWith<$Res>
    implements $BulkOperationsStateCopyWith<$Res> {
  factory _$$BulkOperationsStateImplCopyWith(_$BulkOperationsStateImpl value,
          $Res Function(_$BulkOperationsStateImpl) then) =
      __$$BulkOperationsStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool isActive, Set<String> selectedItems});
}

/// @nodoc
class __$$BulkOperationsStateImplCopyWithImpl<$Res>
    extends _$BulkOperationsStateCopyWithImpl<$Res, _$BulkOperationsStateImpl>
    implements _$$BulkOperationsStateImplCopyWith<$Res> {
  __$$BulkOperationsStateImplCopyWithImpl(_$BulkOperationsStateImpl _value,
      $Res Function(_$BulkOperationsStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of BulkOperationsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isActive = null,
    Object? selectedItems = null,
  }) {
    return _then(_$BulkOperationsStateImpl(
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
      selectedItems: null == selectedItems
          ? _value._selectedItems
          : selectedItems // ignore: cast_nullable_to_non_nullable
              as Set<String>,
    ));
  }
}

/// @nodoc

class _$BulkOperationsStateImpl implements _BulkOperationsState {
  const _$BulkOperationsStateImpl(
      {this.isActive = false, final Set<String> selectedItems = const {}})
      : _selectedItems = selectedItems;

  @override
  @JsonKey()
  final bool isActive;
  final Set<String> _selectedItems;
  @override
  @JsonKey()
  Set<String> get selectedItems {
    if (_selectedItems is EqualUnmodifiableSetView) return _selectedItems;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableSetView(_selectedItems);
  }

  @override
  String toString() {
    return 'BulkOperationsState(isActive: $isActive, selectedItems: $selectedItems)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BulkOperationsStateImpl &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            const DeepCollectionEquality()
                .equals(other._selectedItems, _selectedItems));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isActive,
      const DeepCollectionEquality().hash(_selectedItems));

  /// Create a copy of BulkOperationsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BulkOperationsStateImplCopyWith<_$BulkOperationsStateImpl> get copyWith =>
      __$$BulkOperationsStateImplCopyWithImpl<_$BulkOperationsStateImpl>(
          this, _$identity);
}

abstract class _BulkOperationsState implements BulkOperationsState {
  const factory _BulkOperationsState(
      {final bool isActive,
      final Set<String> selectedItems}) = _$BulkOperationsStateImpl;

  @override
  bool get isActive;
  @override
  Set<String> get selectedItems;

  /// Create a copy of BulkOperationsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BulkOperationsStateImplCopyWith<_$BulkOperationsStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
