import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../services/web_cache_service.dart';
import '../../features/database/data/services/supabase_service.dart';

part 'web_state_manager.freezed.dart';
part 'web_state_manager.g.dart';

/// Web-specific state manager for browser optimizations and desktop workflows
@riverpod
class WebStateManager extends _$WebStateManager {
  @override
  AsyncValue<WebStateManagerState> build() {
    return const AsyncValue.data(WebStateManagerState());
  }

  /// Initialize the web state manager
  Future<void> initialize() async {
    state = const AsyncValue.loading();
    
    try {
      final cacheService = ref.read(webCacheServiceProvider);
      await cacheService.initialize();
      
      state = AsyncValue.data(
        state.value?.copyWith(isInitialized: true) ?? 
        const WebStateManagerState(isInitialized: true),
      );
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Persist state to browser cache
  Future<void> persistState(String key, Map<String, dynamic> data) async {
    try {
      final cacheService = ref.read(webCacheServiceProvider);
      await cacheService.set(key, data);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Restore state from browser cache
  Future<Map<String, dynamic>?> restoreState(String key) async {
    try {
      final cacheService = ref.read(webCacheServiceProvider);
      return await cacheService.get(key);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
      return null;
    }
  }

  /// Add real-time subscription
  Future<void> addSubscription(String key, String tableName) async {
    try {
      final supabaseService = ref.read(supabaseServiceProvider);
      supabaseService.subscribeToTable(tableName, (data) {
        // Handle real-time updates
      });
      
      final currentState = state.value ?? const WebStateManagerState();
      final updatedSubscriptions = Set<String>.from(currentState.activeSubscriptions)
        ..add(key);
      
      state = AsyncValue.data(
        currentState.copyWith(activeSubscriptions: updatedSubscriptions),
      );
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Remove real-time subscription
  Future<void> removeSubscription(String key) async {
    try {
      final currentState = state.value ?? const WebStateManagerState();
      final updatedSubscriptions = Set<String>.from(currentState.activeSubscriptions)
        ..remove(key);
      
      state = AsyncValue.data(
        currentState.copyWith(activeSubscriptions: updatedSubscriptions),
      );
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Set left panel open state
  void setLeftPanelOpen(bool isOpen) {
    final currentState = state.value ?? const WebStateManagerState();
    final updatedLayout = currentState.desktopLayout.copyWith(leftPanelOpen: isOpen);
    
    state = AsyncValue.data(
      currentState.copyWith(desktopLayout: updatedLayout),
    );
  }

  /// Set right panel open state
  void setRightPanelOpen(bool isOpen) {
    final currentState = state.value ?? const WebStateManagerState();
    final updatedLayout = currentState.desktopLayout.copyWith(rightPanelOpen: isOpen);
    
    state = AsyncValue.data(
      currentState.copyWith(desktopLayout: updatedLayout),
    );
  }

  /// Set left panel width
  void setLeftPanelWidth(double width) {
    final currentState = state.value ?? const WebStateManagerState();
    final updatedLayout = currentState.desktopLayout.copyWith(leftPanelWidth: width);
    
    state = AsyncValue.data(
      currentState.copyWith(desktopLayout: updatedLayout),
    );
  }

  /// Set right panel width
  void setRightPanelWidth(double width) {
    final currentState = state.value ?? const WebStateManagerState();
    final updatedLayout = currentState.desktopLayout.copyWith(rightPanelWidth: width);
    
    state = AsyncValue.data(
      currentState.copyWith(desktopLayout: updatedLayout),
    );
  }

  /// Start bulk operation
  void startBulkOperation() {
    final currentState = state.value ?? const WebStateManagerState();
    final updatedBulkOps = currentState.bulkOperations.copyWith(isActive: true);
    
    state = AsyncValue.data(
      currentState.copyWith(bulkOperations: updatedBulkOps),
    );
  }

  /// End bulk operation
  void endBulkOperation() {
    final currentState = state.value ?? const WebStateManagerState();
    final updatedBulkOps = const BulkOperationsState();
    
    state = AsyncValue.data(
      currentState.copyWith(bulkOperations: updatedBulkOps),
    );
  }

  /// Select item for bulk operation
  void selectBulkItem(String itemId) {
    final currentState = state.value ?? const WebStateManagerState();
    final updatedItems = Set<String>.from(currentState.bulkOperations.selectedItems)
      ..add(itemId);
    final updatedBulkOps = currentState.bulkOperations.copyWith(
      selectedItems: updatedItems,
    );
    
    state = AsyncValue.data(
      currentState.copyWith(bulkOperations: updatedBulkOps),
    );
  }

  /// Deselect item from bulk operation
  void deselectBulkItem(String itemId) {
    final currentState = state.value ?? const WebStateManagerState();
    final updatedItems = Set<String>.from(currentState.bulkOperations.selectedItems)
      ..remove(itemId);
    final updatedBulkOps = currentState.bulkOperations.copyWith(
      selectedItems: updatedItems,
    );
    
    state = AsyncValue.data(
      currentState.copyWith(bulkOperations: updatedBulkOps),
    );
  }

  /// Cleanup state and subscriptions
  Future<void> cleanup() async {
    try {
      final cacheService = ref.read(webCacheServiceProvider);
      await cacheService.clear();
      
      state = const AsyncValue.data(WebStateManagerState());
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

/// Desktop layout state provider
@riverpod
class DesktopLayoutNotifier extends _$DesktopLayoutNotifier {
  @override
  DesktopLayoutState build() {
    return const DesktopLayoutState();
  }

  /// Toggle left panel
  void toggleLeftPanel() {
    state = state.copyWith(leftPanelOpen: !state.leftPanelOpen);
  }

  /// Toggle right panel
  void toggleRightPanel() {
    state = state.copyWith(rightPanelOpen: !state.rightPanelOpen);
  }

  /// Set left panel width
  void setLeftPanelWidth(double width) {
    state = state.copyWith(leftPanelWidth: width);
  }

  /// Set right panel width
  void setRightPanelWidth(double width) {
    state = state.copyWith(rightPanelWidth: width);
  }

  /// Reset layout to defaults
  void resetLayout() {
    state = const DesktopLayoutState();
  }
}

/// Web state manager state model
@freezed
class WebStateManagerState with _$WebStateManagerState {
  const factory WebStateManagerState({
    @Default(false) bool isInitialized,
    @Default({}) Set<String> activeSubscriptions,
    @Default(DesktopLayoutState()) DesktopLayoutState desktopLayout,
    @Default(BulkOperationsState()) BulkOperationsState bulkOperations,
  }) = _WebStateManagerState;
}

/// Desktop layout state model
@freezed
class DesktopLayoutState with _$DesktopLayoutState {
  const factory DesktopLayoutState({
    @Default(false) bool leftPanelOpen,
    @Default(false) bool rightPanelOpen,
    @Default(280.0) double leftPanelWidth,
    @Default(320.0) double rightPanelWidth,
  }) = _DesktopLayoutState;
}

/// Bulk operations state model
@freezed
class BulkOperationsState with _$BulkOperationsState {
  const factory BulkOperationsState({
    @Default(false) bool isActive,
    @Default({}) Set<String> selectedItems,
  }) = _BulkOperationsState;
}
